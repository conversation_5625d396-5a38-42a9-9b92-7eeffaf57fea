/* ===================================
   HERO SECTION - PROFESSIONAL ENERGY DESIGN
   Advanced animations and effects for energy industry
   =================================== */

/* Hero Section Base - Improved Layout */
.hero-section-professional {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 120px 0 80px;
    background: linear-gradient(135deg,
            rgba(0, 31, 63, 1) 0%,
            rgba(0, 15, 30, 1) 50%,
            rgba(0, 31, 63, 1) 100%);
}

/* Advanced Background System */
.hero-background-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-primary-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center,
            rgba(0, 163, 255, 0.1) 0%,
            rgba(0, 255, 209, 0.05) 50%,
            transparent 70%);
    animation: gradientPulse 8s ease-in-out infinite;
}

/* Energy Field Animation */
.energy-field-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    height: 800px;
    z-index: 2;
}

.energy-ring {
    position: absolute;
    border: 1px solid rgba(0, 255, 209, 0.3);
    border-radius: 50%;
    animation: energyRotate 20s linear infinite;
}

.energy-ring.ring-1 {
    width: 200px;
    height: 200px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-color: rgba(0, 255, 209, 0.4);
    animation-duration: 15s;
}

.energy-ring.ring-2 {
    width: 400px;
    height: 400px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-color: rgba(0, 163, 255, 0.3);
    animation-duration: 25s;
    animation-direction: reverse;
}

.energy-ring.ring-3 {
    width: 600px;
    height: 600px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-color: rgba(0, 255, 209, 0.2);
    animation-duration: 35s;
}

.energy-ring.ring-4 {
    width: 800px;
    height: 800px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-color: rgba(0, 163, 255, 0.1);
    animation-duration: 45s;
    animation-direction: reverse;
}

/* Particle System */
.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
}

.particle-layer {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle-layer::before {
    content: '';
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(0, 255, 209, 0.6);
    border-radius: 50%;
    box-shadow:
        0 0 6px rgba(0, 255, 209, 0.8),
        0 0 12px rgba(0, 255, 209, 0.4);
    animation: particleFloat 6s ease-in-out infinite;
}

.particle-layer.layer-1::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.particle-layer.layer-2::before {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
    background: rgba(0, 163, 255, 0.6);
    box-shadow:
        0 0 6px rgba(0, 163, 255, 0.8),
        0 0 12px rgba(0, 163, 255, 0.4);
}

.particle-layer.layer-3::before {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

/* Geometric Patterns */
.geometric-patterns {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    opacity: 0.1;
}

.pattern-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 209, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 209, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 30s linear infinite;
}

/* Energy Waves */
.energy-waves {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 200px;
    z-index: 2;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(90deg,
            transparent 0%,
            rgba(0, 255, 209, 0.1) 50%,
            transparent 100%);
    animation: waveMove 8s ease-in-out infinite;
}

.wave.wave-2 {
    animation-delay: 2s;
    background: linear-gradient(90deg,
            transparent 0%,
            rgba(0, 163, 255, 0.1) 50%,
            transparent 100%);
}

.wave.wave-3 {
    animation-delay: 4s;
    height: 50px;
}

/* Hero Container - Improved Layout */
.hero-container-professional {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 0 auto;
    padding: 60px 40px 40px;
    text-align: center;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 160px);
    box-sizing: border-box;
}

/* Logo Section - Improved Positioning */
.hero-logo-section {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo-container-advanced {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
    max-width: 150px;
    max-height: 150px;
}

.logo-glow-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle,
            rgba(0, 255, 209, 0.3) 0%,
            rgba(0, 163, 255, 0.2) 50%,
            transparent 70%);
    border-radius: 50%;
    animation: logoGlow 4s ease-in-out infinite;
    z-index: 1;
}

.logo-energy-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.logo-ring {
    position: absolute;
    border: 1px solid rgba(0, 255, 209, 0.4);
    border-radius: 50%;
    animation: logoRingRotate 10s linear infinite;
}

.logo-ring.ring-1 {
    width: 120px;
    height: 120px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.logo-ring.ring-2 {
    width: 160px;
    height: 160px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-direction: reverse;
    animation-duration: 15s;
}

.logo-ring.ring-3 {
    width: 200px;
    height: 200px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 20s;
}

.hero-logo-main {
    position: relative;
    z-index: 5;
    width: 120px;
    height: auto;
    max-width: 100%;
    filter: drop-shadow(0 0 20px rgba(0, 255, 209, 0.5));
    animation: logoFloat 6s ease-in-out infinite;
}

.logo-pulse-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: rgba(0, 255, 209, 0.2);
    border-radius: 50%;
    animation: logoPulse 3s ease-in-out infinite;
    z-index: 3;
}

/* Title Section */
.hero-title-professional {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 30px;
    color: var(--text-light);
}

.title-line-1 {
    display: block;
    font-size: 1.2em;
    margin-bottom: 10px;
    opacity: 0;
    transform: translateY(30px);
    animation: titleSlideIn 1s ease-out 0.5s forwards;
}

.title-line-2 {
    display: block;
    font-size: 1.5em;
    margin-bottom: 15px;
    opacity: 0;
    transform: translateY(30px);
    animation: titleSlideIn 1s ease-out 0.8s forwards;
}

.title-line-3 {
    display: block;
    font-size: 0.6em;
    font-weight: 500;
    opacity: 0;
    transform: translateY(30px);
    animation: titleSlideIn 1s ease-out 1.1s forwards;
}

.gradient-text-advanced {
    background: linear-gradient(135deg,
            var(--primary) 0%,
            var(--accent) 50%,
            var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.title-underline-effect {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    margin: 20px auto;
    border-radius: 2px;
    animation: underlineGrow 1s ease-out 1.4s forwards;
    transform: scaleX(0);
}

/* Description */
.hero-description-professional {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto 40px;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 1s ease-out 1.6s forwards;
}

/* Stats Section */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin: 50px 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stat-item {
    text-align: center;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 1s ease-out forwards;
}

.stat-item:nth-child(1) {
    animation-delay: 1.8s;
}

.stat-item:nth-child(2) {
    animation-delay: 2s;
}

.stat-item:nth-child(3) {
    animation-delay: 2.2s;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--accent);
    margin-bottom: 8px;
    text-shadow: 0 0 20px rgba(0, 255, 209, 0.5);
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* CTA Buttons */
.cta-buttons-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin: 50px 0;
}

.cta-primary-advanced,
.cta-secondary-advanced {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    overflow: hidden;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 1s ease-out 2.4s forwards;
}

.cta-primary-advanced {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: var(--text-light);
    box-shadow: 0 8px 32px rgba(0, 163, 255, 0.3);
}

.cta-secondary-advanced {
    background: transparent;
    color: var(--text-light);
    border: 2px solid rgba(0, 255, 209, 0.5);
    animation-delay: 2.6s;
}

.button-content {
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 2;
    position: relative;
}

.button-icon svg {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.cta-primary-advanced:hover .button-icon svg,
.cta-secondary-advanced:hover .button-icon svg {
    transform: translateX(5px);
}

.button-glow-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.2),
            rgba(0, 163, 255, 0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50px;
}

.cta-primary-advanced:hover .button-glow-effect {
    opacity: 1;
}

.button-border-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid var(--accent);
    border-radius: 50px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cta-secondary-advanced:hover .button-border-effect {
    opacity: 1;
}

/* Floating Elements */
.hero-floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 4;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgba(0, 255, 209, 0.6);
    border-radius: 50%;
    animation: floatingMove 8s ease-in-out infinite;
}

.floating-element.element-1 {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.floating-element.element-2 {
    top: 70%;
    right: 20%;
    animation-delay: 2s;
    background: rgba(0, 163, 255, 0.6);
}

.floating-element.element-3 {
    bottom: 25%;
    left: 25%;
    animation-delay: 4s;
}

.floating-element.element-4 {
    top: 40%;
    right: 30%;
    animation-delay: 6s;
    background: rgba(0, 163, 255, 0.6);
}

.element-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: inherit;
    border-radius: 50%;
    filter: blur(4px);
    opacity: 0.5;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    animation: scrollBounce 2s ease-in-out infinite;
}

.scroll-text {
    font-size: 0.9rem;
    margin-bottom: 10px;
    font-weight: 500;
}

.scroll-arrow svg {
    width: 24px;
    height: 24px;
    animation: arrowBounce 2s ease-in-out infinite;
}

/* Animations */
@keyframes gradientPulse {

    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }

    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

@keyframes energyRotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes particleFloat {

    0%,
    100% {
        transform: translateY(0px);
        opacity: 0.6;
    }

    50% {
        transform: translateY(-20px);
        opacity: 1;
    }
}

@keyframes gridMove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(50px, 50px);
    }
}

@keyframes waveMove {

    0%,
    100% {
        transform: translateX(-100%);
        opacity: 0;
    }

    50% {
        transform: translateX(0%);
        opacity: 1;
    }
}

@keyframes logoGlow {

    0%,
    100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes logoRingRotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes logoFloat {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

@keyframes logoPulse {

    0%,
    100% {
        opacity: 0.2;
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@keyframes titleSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

@keyframes underlineGrow {
    to {
        transform: scaleX(1);
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes floatingMove {

    0%,
    100% {
        transform: translate(0, 0);
    }

    25% {
        transform: translate(10px, -10px);
    }

    50% {
        transform: translate(-5px, -20px);
    }

    75% {
        transform: translate(-10px, -5px);
    }
}

@keyframes scrollBounce {

    0%,
    100% {
        transform: translateX(-50%) translateY(0);
    }

    50% {
        transform: translateX(-50%) translateY(-10px);
    }
}

@keyframes arrowBounce {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(5px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container-professional {
        padding: 0 20px;
    }

    .hero-logo-main {
        width: 80px;
    }

    .logo-glow-effect,
    .logo-ring.ring-3 {
        width: 150px;
        height: 150px;
    }

    .logo-ring.ring-2 {
        width: 120px;
        height: 120px;
    }

    .logo-ring.ring-1 {
        width: 90px;
        height: 90px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .cta-buttons-container {
        flex-direction: column;
        align-items: center;
    }

    .cta-primary-advanced,
    .cta-secondary-advanced {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .energy-ring {
        display: none;
    }

    .energy-ring.ring-1 {
        display: block;
        width: 150px;
        height: 150px;
    }

    .floating-element {
        display: none;
    }
}

@media (max-width: 480px) {
    .hero-section-professional {
        min-height: 90vh;
    }

    .hero-logo-main {
        width: 60px;
    }

    .hero-title-professional {
        font-size: 2rem;
    }

    .title-line-2 {
        font-size: 1.3em;
    }

    .stat-number {
        font-size: 2rem;
    }

    .scroll-indicator {
        bottom: 20px;
    }
}