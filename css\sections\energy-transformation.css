/* ===================================
   ENERGY TRANSFORMATION ANIMATION
   Smoke to Clean Energy Conversion
   =================================== */

.energy-transformation-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    z-index: 3;
    pointer-events: none;
}

/* ===================================
   POLLUTION/SMOKE PARTICLES
   =================================== */

.pollution-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.smoke-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, 
        rgba(100, 100, 100, 0.6) 0%, 
        rgba(80, 80, 80, 0.4) 50%, 
        transparent 100%
    );
    border-radius: 50%;
    animation: smokeRise 4s ease-out infinite;
}

.smoke-particle.particle-1 {
    top: 70%;
    left: 20%;
    animation-delay: 0s;
    animation-duration: 3.5s;
}

.smoke-particle.particle-2 {
    top: 75%;
    left: 30%;
    animation-delay: 0.8s;
    animation-duration: 4s;
}

.smoke-particle.particle-3 {
    top: 72%;
    left: 70%;
    animation-delay: 1.6s;
    animation-duration: 3.8s;
}

.smoke-particle.particle-4 {
    top: 78%;
    left: 80%;
    animation-delay: 2.4s;
    animation-duration: 4.2s;
}

@keyframes smokeRise {
    0% {
        transform: translateY(0) scale(0.5);
        opacity: 0.8;
        background: radial-gradient(circle, 
            rgba(100, 100, 100, 0.6) 0%, 
            rgba(80, 80, 80, 0.4) 50%, 
            transparent 100%
        );
    }
    50% {
        transform: translateY(-80px) scale(1);
        opacity: 0.6;
        background: radial-gradient(circle, 
            rgba(120, 120, 120, 0.4) 0%, 
            rgba(100, 100, 100, 0.2) 50%, 
            transparent 100%
        );
    }
    100% {
        transform: translateY(-150px) scale(1.5);
        opacity: 0;
        background: radial-gradient(circle, 
            rgba(140, 140, 140, 0.2) 0%, 
            rgba(120, 120, 120, 0.1) 50%, 
            transparent 100%
        );
    }
}

/* ===================================
   TRANSFORMATION CORE
   =================================== */

.transformation-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
}

.core-inner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: radial-gradient(circle,
        rgba(0, 255, 209, 0.8) 0%,
        rgba(0, 163, 255, 0.6) 50%,
        rgba(138, 43, 226, 0.4) 100%
    );
    border-radius: 50%;
    animation: coreTransformation 3s ease-in-out infinite;
}

.core-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: radial-gradient(circle,
        rgba(0, 255, 209, 0.3) 0%,
        rgba(0, 163, 255, 0.2) 50%,
        transparent 100%
    );
    border-radius: 50%;
    animation: coreGlowPulse 2s ease-in-out infinite;
}

@keyframes coreTransformation {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        box-shadow: 
            0 0 20px rgba(0, 255, 209, 0.5),
            0 0 40px rgba(0, 255, 209, 0.3);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        box-shadow: 
            0 0 30px rgba(0, 255, 209, 0.8),
            0 0 60px rgba(0, 255, 209, 0.5),
            0 0 90px rgba(0, 163, 255, 0.3);
    }
}

@keyframes coreGlowPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.3;
    }
}

/* ===================================
   CLEAN ENERGY OUTPUT
   =================================== */

.clean-energy-output {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.energy-beam {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 80px;
    background: linear-gradient(to top,
        rgba(0, 255, 209, 0.8) 0%,
        rgba(0, 255, 209, 0.4) 50%,
        transparent 100%
    );
    transform-origin: bottom center;
    animation: energyBeamPulse 2.5s ease-in-out infinite;
}

.energy-beam.beam-1 {
    transform: translate(-50%, -50%) rotate(0deg);
    animation-delay: 0s;
}

.energy-beam.beam-2 {
    transform: translate(-50%, -50%) rotate(120deg);
    animation-delay: 0.8s;
}

.energy-beam.beam-3 {
    transform: translate(-50%, -50%) rotate(240deg);
    animation-delay: 1.6s;
}

@keyframes energyBeamPulse {
    0%, 100% {
        opacity: 0.3;
        height: 60px;
        box-shadow: 0 0 10px rgba(0, 255, 209, 0.3);
    }
    50% {
        opacity: 0.8;
        height: 100px;
        box-shadow: 0 0 20px rgba(0, 255, 209, 0.6);
    }
}

.energy-wave {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(0, 255, 209, 0.4);
    border-radius: 50%;
    animation: energyWaveExpand 3s ease-out infinite;
}

.energy-wave.wave-1 {
    animation-delay: 0s;
}

.energy-wave.wave-2 {
    animation-delay: 1.5s;
}

@keyframes energyWaveExpand {
    0% {
        width: 40px;
        height: 40px;
        opacity: 0.8;
        border-color: rgba(0, 255, 209, 0.6);
    }
    50% {
        width: 120px;
        height: 120px;
        opacity: 0.4;
        border-color: rgba(0, 255, 209, 0.3);
    }
    100% {
        width: 200px;
        height: 200px;
        opacity: 0;
        border-color: rgba(0, 255, 209, 0.1);
    }
}

/* ===================================
   RESPONSIVE ADJUSTMENTS
   =================================== */

@media (max-width: 768px) {
    .energy-transformation-container {
        width: 200px;
        height: 200px;
    }
    
    .core-inner {
        width: 30px;
        height: 30px;
    }
    
    .core-glow {
        width: 45px;
        height: 45px;
    }
    
    .energy-beam {
        height: 60px;
    }
    
    .smoke-particle {
        width: 6px;
        height: 6px;
    }
}

@media (max-width: 480px) {
    .energy-transformation-container {
        width: 150px;
        height: 150px;
    }
    
    .core-inner {
        width: 25px;
        height: 25px;
    }
    
    .core-glow {
        width: 35px;
        height: 35px;
    }
    
    .energy-beam {
        height: 45px;
    }
    
    .smoke-particle {
        width: 4px;
        height: 4px;
    }
}
