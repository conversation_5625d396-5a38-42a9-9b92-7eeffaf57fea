/* Multilingual Support Styles */

/* RTL/LTR Base Styles */
html[dir="rtl"],
body.rtl,
.rtl {
    direction: rtl;
    text-align: right;
}

html[dir="ltr"],
body.ltr,
.ltr {
    direction: ltr;
    text-align: left;
}

/* Global RTL/LTR Typography */
.rtl h1,
.rtl h2,
.rtl h3,
.rtl h4,
.rtl h5,
.rtl h6,
.rtl p,
.rtl div,
.rtl span,
.rtl li {
    text-align: right;
}

.ltr h1,
.ltr h2,
.ltr h3,
.ltr h4,
.ltr h5,
.ltr h6,
.ltr p,
.ltr div,
.ltr span,
.ltr li {
    text-align: left;
}

/* Centered elements should remain centered */
.rtl .section-header,
.rtl .hero-stats,
.rtl .process-step,
.ltr .section-header,
.ltr .hero-stats,
.ltr .process-step {
    text-align: center;
}

/* Font Switching */
.lang-fa {
    font-family: 'Vazirmatn', sans-serif;
}

.lang-en {
    font-family: 'Inter', 'Roboto', sans-serif;
}

/* Language Toggle Button */
.lang-switch {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 25px;
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.lang-switch:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 163, 255, 0.3);
}

.lang-switch .icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.lang-switch .lang-text {
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
}

/* Loading State */
.lang-switch:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid var(--accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Language Transition Effects */
.transitioning {
    transition: all 0.3s ease;
}

/* RTL Specific Adjustments */
/* Header */
.rtl .header-logo {
    flex-direction: row-reverse;
}

.rtl .nav-list {
    flex-direction: row-reverse;
}

.rtl .header-actions {
    flex-direction: row-reverse;
}

.rtl .btn-arrow {
    transform: scaleX(-1);
}

/* Hero Section */
.rtl .hero-layout,
.rtl .hero-grid {
    direction: rtl;
}

.rtl .hero-content-section {
    text-align: right;
}

.rtl .hero-actions {
    flex-direction: row-reverse;
}

.rtl .features-grid {
    direction: rtl;
}

.rtl .feature-item {
    flex-direction: row-reverse;
}

/* About Section */
.rtl .about-content-grid {
    direction: rtl;
}

.rtl .story-header,
.rtl .values-header {
    flex-direction: row-reverse;
}

.rtl .timeline-item {
    flex-direction: row-reverse;
}

.rtl .value-item {
    flex-direction: row-reverse;
}

/* Services Section */
.rtl .services-grid {
    direction: rtl;
}

.rtl .service-header {
    flex-direction: row-reverse;
}

.rtl .service-features li {
    flex-direction: row-reverse;
}

/* Projects Section */
.rtl .projects-grid {
    direction: rtl;
}

.rtl .project-card {
    text-align: right;
}

/* Activities Section */
.rtl .activities-grid {
    direction: rtl;
}

.rtl .activity-card {
    text-align: right;
}

.rtl .card-header {
    flex-direction: row-reverse;
}

.rtl .activity-features {
    direction: rtl;
}

.rtl .feature-item {
    flex-direction: row-reverse;
}

/* Projects Section */
.rtl .projects-grid {
    direction: rtl;
}

.rtl .project-card {
    text-align: right;
}

.rtl .project-header {
    flex-direction: row-reverse;
}

/* Rebranding Section */
.rtl .rebranding-content {
    direction: rtl;
}

.rtl .rebranding-content-grid {
    direction: rtl;
}

.rtl .timeline-container {
    direction: rtl;
}

.rtl .timeline-item {
    flex-direction: row-reverse;
}

.rtl .brand-evolution-section {
    text-align: right;
}

/* Contact Section */
.rtl .contact-content {
    flex-direction: row-reverse;
}

.rtl .form-grid {
    direction: rtl;
}

.rtl .submit-btn {
    flex-direction: row-reverse;
}

.rtl .contact-info {
    text-align: right;
}

.rtl .info-item {
    flex-direction: row-reverse;
}

.rtl .hero-grid {
    flex-direction: row-reverse;
}

.rtl .about-flex {
    flex-direction: row-reverse;
}

.rtl .contact-content {
    flex-direction: row-reverse;
}

/* LTR Specific Adjustments */
/* Header */
.ltr .header-logo {
    flex-direction: row;
}

.ltr .nav-list {
    flex-direction: row;
}

.ltr .header-actions {
    flex-direction: row;
}

.ltr .btn-arrow {
    transform: scaleX(1);
}

/* Hero Section */
.ltr .hero-layout,
.ltr .hero-grid {
    direction: ltr;
}

.ltr .hero-content-section {
    text-align: left;
}

.ltr .hero-actions {
    flex-direction: row;
}

.ltr .features-grid {
    direction: ltr;
}

.ltr .feature-item {
    flex-direction: row;
}

/* About Section */
.ltr .about-content-grid {
    direction: ltr;
}

.ltr .story-header,
.ltr .values-header {
    flex-direction: row;
}

.ltr .timeline-item {
    flex-direction: row;
}

.ltr .value-item {
    flex-direction: row;
}

/* Services Section */
.ltr .services-grid {
    direction: ltr;
}

.ltr .service-header {
    flex-direction: row;
}

.ltr .service-features li {
    flex-direction: row;
}

/* Projects Section */
.ltr .projects-grid {
    direction: ltr;
}

.ltr .project-card {
    text-align: left;
}

/* Activities Section */
.ltr .activities-grid {
    direction: ltr;
}

.ltr .activity-card {
    text-align: left;
}

.ltr .card-header {
    flex-direction: row;
}

.ltr .activity-features {
    direction: ltr;
}

.ltr .feature-item {
    flex-direction: row;
}

/* Projects Section */
.ltr .projects-grid {
    direction: ltr;
}

.ltr .project-card {
    text-align: left;
}

.ltr .project-header {
    flex-direction: row;
}

/* Rebranding Section */
.ltr .rebranding-content {
    direction: ltr;
}

.ltr .rebranding-content-grid {
    direction: ltr;
}

.ltr .timeline-container {
    direction: ltr;
}

.ltr .timeline-item {
    flex-direction: row;
}

.ltr .brand-evolution-section {
    text-align: left;
}

/* Contact Section */
.ltr .contact-content {
    flex-direction: row;
}

.ltr .form-grid {
    direction: ltr;
}

.ltr .submit-btn {
    flex-direction: row;
}

.ltr .contact-info {
    text-align: left;
}

.ltr .info-item {
    flex-direction: row;
}

/* Text Alignment Adjustments */
.rtl .section-title,
.rtl .section-subtitle,
.rtl .about-title,
.rtl .values-title,
.rtl .mission-title {
    text-align: right;
}

.ltr .section-title,
.ltr .section-subtitle,
.ltr .about-title,
.ltr .values-title,
.ltr .mission-title {
    text-align: left;
}

/* Navigation Adjustments */
.rtl .nav-link {
    padding: 12px 0 12px 20px;
}

.ltr .nav-link {
    padding: 12px 20px 12px 0;
}

/* Form Adjustments */
.rtl .form-group label {
    text-align: right;
}

.ltr .form-group label {
    text-align: left;
}

.rtl .form-group input,
.rtl .form-group textarea,
.rtl .form-group select {
    text-align: right;
}

.ltr .form-group input,
.ltr .form-group textarea,
.ltr .form-group select {
    text-align: left;
}

/* Card Content Adjustments */
.rtl .feature-content,
.rtl .service-content,
.rtl .project-content {
    text-align: right;
}

.ltr .feature-content,
.ltr .service-content,
.ltr .project-content {
    text-align: left;
}

/* Statistics Adjustments */
.rtl .stat-content {
    text-align: right;
}

.ltr .stat-content {
    text-align: left;
}

/* Timeline Adjustments */
.rtl .timeline-content {
    text-align: right;
}

.ltr .timeline-content {
    text-align: left;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .lang-switch {
        padding: 6px 12px;
        font-size: 12px;
    }

    .lang-switch .icon {
        width: 14px;
        height: 14px;
    }

    .lang-switch .lang-text {
        font-size: 12px;
    }

    /* Mobile RTL/LTR Adjustments - Center align on mobile */
    .rtl .hero-content-section,
    .ltr .hero-content-section,
    .rtl .service-card,
    .ltr .service-card,
    .rtl .activity-card,
    .ltr .activity-card,
    .rtl .project-card,
    .ltr .project-card {
        text-align: center;
    }

    /* Mobile Layout Adjustments */
    .rtl .hero-layout,
    .rtl .hero-grid,
    .rtl .about-content-grid,
    .rtl .contact-content,
    .ltr .hero-layout,
    .ltr .hero-grid,
    .ltr .about-content-grid,
    .ltr .contact-content {
        flex-direction: column;
    }

    .rtl .hero-actions,
    .ltr .hero-actions,
    .rtl .service-header,
    .ltr .service-header {
        flex-direction: column;
        align-items: center;
    }

    .rtl .feature-item,
    .ltr .feature-item,
    .rtl .timeline-item,
    .ltr .timeline-item,
    .rtl .value-item,
    .ltr .value-item {
        flex-direction: column;
        text-align: center;
    }
}

/* Animation Adjustments for RTL */
.rtl [data-aos="fade-left"] {
    transform: translate3d(-100px, 0, 0);
}

.rtl [data-aos="fade-right"] {
    transform: translate3d(100px, 0, 0);
}

.ltr [data-aos="fade-left"] {
    transform: translate3d(100px, 0, 0);
}

.ltr [data-aos="fade-right"] {
    transform: translate3d(-100px, 0, 0);
}

/* Language-specific Typography */
.lang-fa h1,
.lang-fa h2,
.lang-fa h3,
.lang-fa h4,
.lang-fa h5,
.lang-fa h6 {
    font-weight: 700;
    line-height: 1.4;
}

.lang-en h1,
.lang-en h2,
.lang-en h3,
.lang-en h4,
.lang-en h5,
.lang-en h6 {
    font-weight: 600;
    line-height: 1.2;
}

.lang-fa p,
.lang-fa span,
.lang-fa div {
    line-height: 1.8;
}

.lang-en p,
.lang-en span,
.lang-en div {
    line-height: 1.6;
}

/* Language Toggle Animation */
.lang-switch::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--primary), var(--accent));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 25px;
    z-index: -1;
}

.lang-switch:hover::before {
    opacity: 0.1;
}

/* Smooth Language Transition */
* {
    transition: direction 0.3s ease, text-align 0.3s ease;
}

/* Language Badge */
.lang-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--accent);
    color: var(--secondary);
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    line-height: 1;
}

.rtl .lang-badge {
    right: auto;
    left: -8px;
}