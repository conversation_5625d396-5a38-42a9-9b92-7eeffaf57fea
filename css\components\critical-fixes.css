/* ===================================
   CRITICAL FIXES FOR INOVA ENERGY THEME
   Fixes for blue page, header overlay, and animation issues
   =================================== */

/* Prevent Blue Page Issue */
body {
    background: var(--secondary) !important;
    color: var(--text-light) !important;
    overflow-x: hidden;
}

/* Ensure proper body background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(0, 31, 63, 1) 0%, 
        rgba(0, 15, 30, 1) 50%,
        rgba(0, 31, 63, 1) 100%);
    z-index: -1000;
    pointer-events: none;
}

/* Fix Header Overlay on Hero */
.site-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    z-index: 1000 !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Header States */
.site-header.hero-overlay {
    background: linear-gradient(135deg, 
        rgba(0, 15, 30, 0.7) 0%, 
        rgba(0, 31, 63, 0.7) 50%,
        rgba(0, 15, 30, 0.7) 100%) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    border-bottom: 1px solid rgba(0, 255, 209, 0.1) !important;
    box-shadow: 0 2px 20px rgba(0, 163, 255, 0.05) !important;
}

.site-header.scrolled {
    background: linear-gradient(135deg, 
        rgba(0, 15, 30, 0.95) 0%, 
        rgba(0, 31, 63, 0.95) 50%,
        rgba(0, 15, 30, 0.95) 100%) !important;
    backdrop-filter: blur(25px) !important;
    -webkit-backdrop-filter: blur(25px) !important;
    box-shadow: 0 8px 40px rgba(0, 163, 255, 0.15) !important;
    border-bottom-color: rgba(0, 255, 209, 0.25) !important;
}

/* Hero Section Adjustments */
.hero-section-professional {
    padding-top: 0 !important;
    min-height: 100vh !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Prevent Animation Conflicts */
* {
    animation-fill-mode: both !important;
}

/* Fix Canvas Issues */
canvas {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    pointer-events: none !important;
    z-index: 1 !important;
}

/* Ensure Content is Above Background */
.hero-container-professional {
    position: relative !important;
    z-index: 10 !important;
    padding-top: 100px !important;
}

/* Fix Logo Positioning */
.hero-logo-section {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-bottom: 30px !important;
}

.logo-container-advanced {
    max-width: 150px !important;
    max-height: 150px !important;
    position: relative !important;
    display: inline-block !important;
}

.hero-logo-main {
    width: 120px !important;
    height: auto !important;
    max-width: 100% !important;
    display: block !important;
}

/* Fix Text Alignment for RTL/LTR */
.rtl .hero-section-professional {
    direction: rtl !important;
    text-align: right !important;
}

.ltr .hero-section-professional {
    direction: ltr !important;
    text-align: left !important;
}

.rtl .hero-container-professional {
    text-align: center !important;
}

.ltr .hero-container-professional {
    text-align: center !important;
}

/* Fix Button Layouts */
.rtl .cta-buttons-container {
    flex-direction: row-reverse !important;
    justify-content: center !important;
    gap: 1rem !important;
}

.ltr .cta-buttons-container {
    flex-direction: row !important;
    justify-content: center !important;
    gap: 1rem !important;
}

/* Prevent Overflow Issues */
.hero-title-professional,
.hero-description-professional,
.stats-grid {
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

/* Fix Stats Grid */
.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 2rem !important;
    justify-items: center !important;
    margin: 2rem 0 !important;
}

/* Mobile Responsive Fixes */
@media (max-width: 768px) {
    .hero-container-professional {
        padding: 120px 20px 40px !important;
    }
    
    .stats-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
    
    .cta-buttons-container {
        flex-direction: column !important;
        align-items: center !important;
        gap: 1rem !important;
    }
    
    .hero-logo-main {
        width: 100px !important;
    }
}

/* Fix Language Transition */
body.transitioning * {
    transition: all 0.3s ease !important;
}

/* Ensure Proper Z-Index Stacking */
.hero-background-system {
    z-index: 1 !important;
}

.hero-container-professional {
    z-index: 10 !important;
}

.site-header {
    z-index: 1000 !important;
}

/* Fix Scroll Indicator */
.scroll-indicator {
    position: absolute !important;
    bottom: 30px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 15 !important;
}
