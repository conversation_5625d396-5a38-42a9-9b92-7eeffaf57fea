/* ===================================
   SERVICES SECTION - PROFESSIONAL DESIGN
   Advanced styling for services section
   =================================== */

/* Services Section Base */
.services-section-professional {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 1) 0%,
            rgba(0, 31, 63, 1) 50%,
            rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
}

/* Background System */
.services-background-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient-primary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center,
            rgba(0, 163, 255, 0.1) 0%,
            rgba(0, 255, 209, 0.05) 50%,
            transparent 70%);
    animation: gradientPulse 8s ease-in-out infinite;
}

.bg-pattern-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 209, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 209, 0.05) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 30s linear infinite;
    opacity: 0.3;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(0, 255, 209, 0.6);
    border-radius: 50%;
    animation: floatingMove 8s ease-in-out infinite;
}

.floating-element.element-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element.element-2 {
    top: 60%;
    right: 15%;
    animation-delay: 3s;
    background: rgba(0, 163, 255, 0.6);
}

.floating-element.element-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 6s;
}

/* Services Container */
.services-container-professional {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.services-header {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 20px;
    background: rgba(0, 255, 209, 0.1);
    border: 1px solid rgba(0, 255, 209, 0.3);
    border-radius: 50px;
    color: var(--accent);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 1.2rem;
}

.section-title-professional {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 30px;
    color: var(--text-light);
}

.title-line-1 {
    display: block;
    font-size: 1em;
    margin-bottom: 10px;
    opacity: 0;
    transform: translateY(30px);
    animation: titleSlideIn 1s ease-out 0.5s forwards;
}

.title-line-2 {
    display: block;
    font-size: 1.2em;
    margin-bottom: 10px;
    opacity: 0;
    transform: translateY(30px);
    animation: titleSlideIn 1s ease-out 0.8s forwards;
}

.title-line-3 {
    display: block;
    font-size: 0.7em;
    font-weight: 500;
    opacity: 0;
    transform: translateY(30px);
    animation: titleSlideIn 1s ease-out 1.1s forwards;
}

.gradient-text-professional {
    background: linear-gradient(135deg,
            var(--primary) 0%,
            var(--accent) 50%,
            var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.section-description-professional {
    font-size: clamp(1.1rem, 2.5vw, 1.3rem);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    max-width: 700px;
    margin: 0 auto;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 1s ease-out 1.4s forwards;
}

/* Services Grid */
.services-grid-professional {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin: 80px 0;
}

/* Service Item */
.service-item-professional {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease-out forwards;
}

.service-item-professional:nth-child(1) {
    animation-delay: 1.6s;
}

.service-item-professional:nth-child(2) {
    animation-delay: 1.8s;
}

.service-item-professional:nth-child(3) {
    animation-delay: 2s;
}

.service-item-professional:nth-child(4) {
    animation-delay: 2.2s;
}

/* Service Card */
.service-card-professional {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: all 0.4s ease;
    backdrop-filter: blur(20px);
    overflow: hidden;
}

.service-card-professional:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 209, 0.3);
    box-shadow: 0 20px 60px rgba(0, 163, 255, 0.2);
}

.card-background-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.05) 0%,
            rgba(0, 163, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: 20px;
}

.service-card-professional:hover .card-background-effect {
    opacity: 1;
}

/* Card Header */
.card-header-professional {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
}

.service-icon-professional {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
}

.service-icon-professional svg {
    width: 28px;
    height: 28px;
}

.service-card-professional:hover .service-icon-professional {
    transform: scale(1.1);
    box-shadow: 0 10px 30px rgba(0, 163, 255, 0.3);
}

.service-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.05);
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Card Content */
.card-content-professional {
    flex: 1;
    margin-bottom: 30px;
}

.service-title-professional {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 15px;
    line-height: 1.3;
}

.service-description-professional {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 25px;
}

/* Service Features */
.service-features-professional {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.feature-item-professional {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.feature-icon {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.7rem;
    font-weight: 700;
    flex-shrink: 0;
}

/* Card Footer */
.card-footer-professional {
    margin-top: auto;
}

.service-cta-professional {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--accent);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    padding: 8px 0;
}

.service-cta-professional:hover {
    gap: 12px;
    color: var(--primary);
}

.cta-arrow {
    transition: transform 0.3s ease;
}

.service-cta-professional:hover .cta-arrow {
    transform: translateX(4px);
}

/* CTA Section */
.services-cta-section {
    text-align: center;
    margin-top: 80px;
    padding: 60px 40px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(20px);
}

.cta-content-professional {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title-professional {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 15px;
}

.cta-description-professional {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 30px;
}

.cta-button-professional {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: var(--text-light);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    overflow: hidden;
}

.button-content {
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 2;
    position: relative;
}

.button-icon svg {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.cta-button-professional:hover .button-icon svg {
    transform: translateX(4px);
}

.button-glow-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.2),
            rgba(0, 163, 255, 0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50px;
}

.cta-button-professional:hover .button-glow-effect {
    opacity: 1;
}

.cta-button-professional:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(0, 163, 255, 0.3);
}

/* Animations */
@keyframes gradientPulse {

    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }

    50% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

@keyframes gridMove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(50px, 50px);
    }
}

@keyframes floatingMove {

    0%,
    100% {
        transform: translate(0, 0);
    }

    25% {
        transform: translate(10px, -10px);
    }

    50% {
        transform: translate(-5px, -20px);
    }

    75% {
        transform: translate(-10px, -5px);
    }
}

@keyframes titleSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* RTL Support for Services Section */
.rtl .services-section-professional {
    direction: rtl;
    text-align: right;
}

.rtl .services-header {
    text-align: center;
    /* Keep header centered */
}

.rtl .card-header-professional {
    flex-direction: row-reverse;
}

.rtl .feature-item-professional {
    flex-direction: row-reverse;
    text-align: right;
}

.rtl .service-cta-professional {
    flex-direction: row-reverse;
}

.rtl .service-cta-professional:hover .cta-arrow {
    transform: translateX(-4px);
}

.rtl .button-content {
    flex-direction: row-reverse;
}

.rtl .cta-button-professional:hover .button-icon svg {
    transform: translateX(-4px);
}

/* Enhanced Visual Effects */
.service-card-professional::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.02) 0%,
            rgba(0, 163, 255, 0.02) 100%);
    border-radius: 20px;
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.service-card-professional:hover::before {
    opacity: 1;
}

/* Improved Animations */
.service-card-professional {
    transform: translateY(0);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card-professional:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(0, 255, 209, 0.4);
    box-shadow:
        0 20px 60px rgba(0, 163, 255, 0.15),
        0 0 0 1px rgba(0, 255, 209, 0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .services-grid-professional {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .services-section-professional {
        padding: 80px 0;
    }

    .services-container-professional {
        padding: 0 20px;
    }

    .services-header {
        margin-bottom: 60px;
    }

    .services-grid-professional {
        grid-template-columns: 1fr;
        gap: 30px;
        margin: 60px 0;
    }

    .service-card-professional {
        padding: 30px;
    }

    .services-cta-section {
        margin-top: 60px;
        padding: 40px 20px;
    }

    .cta-title-professional {
        font-size: 1.6rem;
    }

    .cta-button-professional {
        width: 100%;
        justify-content: center;
        max-width: 280px;
    }
}

@media (max-width: 480px) {
    .service-card-professional {
        padding: 25px;
    }

    .card-header-professional {
        margin-bottom: 25px;
    }

    .service-icon-professional {
        width: 50px;
        height: 50px;
    }

    .service-icon-professional svg {
        width: 24px;
        height: 24px;
    }

    .service-title-professional {
        font-size: 1.3rem;
    }
}