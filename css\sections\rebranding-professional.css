/* ===================================
   REBRANDING SECTION - PROFESSIONAL DESIGN
   Ultra-advanced styling with cutting-edge animations
   =================================== */

/* Rebranding Section Base - Enhanced */
.rebranding-section-ultimate {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 1) 0%,
            rgba(0, 31, 63, 1) 30%,
            rgba(0, 15, 30, 1) 70%,
            rgba(0, 31, 63, 1) 100%);
    overflow: hidden;
}

/* RTL/LTR Support */
.rtl .rebranding-section-ultimate {
    direction: rtl;
    text-align: right;
}

.ltr .rebranding-section-ultimate {
    direction: ltr;
    text-align: left;
}

/* Advanced Background System */
.rebranding-background-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient-advanced {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse at 20% 30%, rgba(0, 163, 255, 0.15) 0%, transparent 50%),
        radial-gradient(ellipse at 80% 70%, rgba(0, 255, 209, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at 50% 50%, rgba(0, 163, 255, 0.05) 0%, transparent 70%);
    animation: gradientFlow 12s ease-in-out infinite;
}

.bg-pattern-advanced {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(30deg, rgba(0, 255, 209, 0.03) 1px, transparent 1px),
        linear-gradient(120deg, rgba(0, 163, 255, 0.03) 1px, transparent 1px);
    background-size: 60px 60px, 40px 40px;
    animation: patternShift 25s linear infinite;
    opacity: 0.4;
}

.floating-shapes-advanced {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape-advanced {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.1),
            rgba(0, 163, 255, 0.1));
    animation: shapeFloat 15s ease-in-out infinite;
}

.shape-advanced.shape-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.shape-advanced.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 5s;
}

.shape-advanced.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 15%;
    animation-delay: 10s;
}

/* Energy Particles */
.energy-particles-advanced {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle-advanced {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(0, 255, 209, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(0, 255, 209, 0.6);
    animation: particleMove 8s linear infinite;
}

.particle-advanced:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.particle-advanced:nth-child(2) {
    top: 40%;
    left: 80%;
    animation-delay: 2s;
    background: rgba(0, 163, 255, 0.8);
}

.particle-advanced:nth-child(3) {
    top: 70%;
    left: 20%;
    animation-delay: 4s;
}

.particle-advanced:nth-child(4) {
    top: 30%;
    left: 60%;
    animation-delay: 6s;
    background: rgba(0, 163, 255, 0.8);
}

/* Rebranding Container */
.rebranding-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.rebranding-header {
    text-align: center;
    margin-bottom: 100px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 10px 25px;
    background: rgba(0, 255, 209, 0.1);
    border: 1px solid rgba(0, 255, 209, 0.3);
    border-radius: 50px;
    color: var(--accent);
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 40px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 32px rgba(0, 255, 209, 0.1);
    animation: badgePulse 3s ease-in-out infinite;
}

.badge-icon {
    font-size: 1.4rem;
    animation: iconRotate 4s linear infinite;
}

.section-title-ultimate {
    font-size: clamp(2.5rem, 6vw, 5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 40px;
    color: var(--text-light);
    text-shadow: 0 4px 20px rgba(0, 163, 255, 0.3);
}

.title-line-1 {
    display: block;
    font-size: 1em;
    margin-bottom: 15px;
    opacity: 0;
    transform: translateY(50px) rotateX(90deg);
    animation: titleReveal 1.5s ease-out 0.5s forwards;
}

.title-line-2 {
    display: block;
    font-size: 1.3em;
    margin-bottom: 15px;
    opacity: 0;
    transform: translateY(50px) rotateX(90deg);
    animation: titleReveal 1.5s ease-out 0.8s forwards;
}

.gradient-text-ultimate {
    background: linear-gradient(135deg,
            var(--primary) 0%,
            var(--accent) 30%,
            #00FFD1 60%,
            var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 300% 300%;
    animation: gradientAnimation 4s ease-in-out infinite;
}

.section-description-ultimate {
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.85);
    max-width: 800px;
    margin: 0 auto;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1.5s ease-out 1.2s forwards;
}

/* Content Grid */
.rebranding-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: start;
    margin: 100px 0;
}

/* Story Section */
.story-section {
    opacity: 0;
    transform: translateX(-50px);
    animation: slideInLeft 1.5s ease-out 1.5s forwards;
}

.story-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 50px;
    backdrop-filter: blur(25px);
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
}

.story-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.05) 0%,
            rgba(0, 163, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.story-card:hover::before {
    opacity: 1;
}

.story-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 209, 0.3);
    box-shadow: 0 25px 80px rgba(0, 163, 255, 0.2);
}

.story-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

.story-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    box-shadow: 0 10px 30px rgba(0, 163, 255, 0.3);
    animation: iconPulse 3s ease-in-out infinite;
}

.story-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-light);
    margin: 0;
}

.story-content {
    position: relative;
    z-index: 2;
}

.story-description {
    font-size: 1.1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary), var(--accent));
    border-radius: 2px;
    animation: timelineGrow 2s ease-out 2s forwards;
    transform: scaleY(0);
    transform-origin: top;
}

.timeline-item {
    position: relative;
    margin-bottom: 25px;
    opacity: 0;
    transform: translateX(30px);
    animation: timelineItemSlide 1s ease-out forwards;
}

.timeline-item:nth-child(1) {
    animation-delay: 2.2s;
}

.timeline-item:nth-child(2) {
    animation-delay: 2.4s;
}

.timeline-item:nth-child(3) {
    animation-delay: 2.6s;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -37px;
    top: 8px;
    width: 12px;
    height: 12px;
    background: var(--accent);
    border-radius: 50%;
    box-shadow: 0 0 15px rgba(0, 255, 209, 0.6);
    animation: dotPulse 2s ease-in-out infinite;
}

.timeline-year {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--accent);
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

/* Benefits Section */
.benefits-section {
    opacity: 0;
    transform: translateX(50px);
    animation: slideInRight 1.5s ease-out 1.8s forwards;
}

.benefits-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 50px;
    backdrop-filter: blur(25px);
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
}

.benefits-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.05) 0%,
            rgba(0, 255, 209, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.benefits-card:hover::before {
    opacity: 1;
}

.benefits-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 163, 255, 0.3);
    box-shadow: 0 25px 80px rgba(0, 255, 209, 0.2);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 40px;
}

.header-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--accent), var(--primary));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 255, 209, 0.3);
    animation: iconPulse 3s ease-in-out infinite 1s;
}

.header-icon svg {
    width: 32px;
    height: 32px;
}

.card-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-light);
    margin: 0;
}

/* Benefits Grid */
.benefits-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    position: relative;
    z-index: 2;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 15px;
    transition: all 0.4s ease;
    opacity: 0;
    transform: translateY(20px);
    animation: benefitSlideIn 1s ease-out forwards;
}

.benefit-item:nth-child(1) {
    animation-delay: 2.8s;
}

.benefit-item:nth-child(2) {
    animation-delay: 3s;
}

.benefit-item:nth-child(3) {
    animation-delay: 3.2s;
}

.benefit-item:nth-child(4) {
    animation-delay: 3.4s;
}

.benefit-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 255, 209, 0.2);
    transform: translateX(10px);
}

.benefit-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 8px 25px rgba(0, 163, 255, 0.2);
}

.benefit-icon svg {
    width: 24px;
    height: 24px;
}

.benefit-content {
    flex: 1;
}

.benefit-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 8px;
}

.benefit-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
}

/* Animations */
@keyframes gradientFlow {

    0%,
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.3;
    }

    33% {
        transform: scale(1.1) rotate(120deg);
        opacity: 0.5;
    }

    66% {
        transform: scale(0.9) rotate(240deg);
        opacity: 0.4;
    }
}

@keyframes patternShift {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }

    100% {
        transform: translate(60px, 60px) rotate(360deg);
    }
}

@keyframes shapeFloat {

    0%,
    100% {
        transform: translate(0, 0) rotate(0deg);
    }

    25% {
        transform: translate(20px, -20px) rotate(90deg);
    }

    50% {
        transform: translate(-10px, -30px) rotate(180deg);
    }

    75% {
        transform: translate(-20px, -10px) rotate(270deg);
    }
}

@keyframes particleMove {
    0% {
        transform: translate(0, 0);
        opacity: 0;
    }

    10% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    100% {
        transform: translate(100px, -100px);
        opacity: 0;
    }
}

@keyframes badgePulse {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 8px 32px rgba(0, 255, 209, 0.1);
    }

    50% {
        transform: scale(1.05);
        box-shadow: 0 12px 40px rgba(0, 255, 209, 0.2);
    }
}

@keyframes iconRotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes titleReveal {
    to {
        opacity: 1;
        transform: translateY(0) rotateX(0deg);
    }
}

@keyframes gradientAnimation {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes timelineGrow {
    to {
        transform: scaleY(1);
    }
}

@keyframes timelineItemSlide {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes dotPulse {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 0 15px rgba(0, 255, 209, 0.6);
    }

    50% {
        transform: scale(1.2);
        box-shadow: 0 0 25px rgba(0, 255, 209, 0.8);
    }
}

@keyframes iconPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

@keyframes benefitSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .rebranding-content-grid {
        gap: 60px;
    }

    .story-card,
    .benefits-card {
        padding: 40px;
    }
}

@media (max-width: 768px) {
    .rebranding-section-ultimate {
        padding: 80px 0;
    }

    .rebranding-container {
        padding: 0 20px;
    }

    .rebranding-header {
        margin-bottom: 60px;
    }

    .rebranding-content-grid {
        grid-template-columns: 1fr;
        gap: 50px;
        margin: 60px 0;
    }

    .story-card,
    .benefits-card {
        padding: 30px;
    }

    .story-header,
    .card-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .timeline {
        padding-left: 25px;
    }

    .timeline-item::before {
        left: -32px;
    }

    .benefit-item {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }

    /* Hide complex animations on mobile */
    .shape-advanced,
    .particle-advanced {
        display: none;
    }
}

@media (max-width: 480px) {

    .story-card,
    .benefits-card {
        padding: 25px;
    }

    .story-icon,
    .header-icon {
        width: 60px;
        height: 60px;
    }

    .story-title,
    .card-title {
        font-size: 1.5rem;
    }

    .benefit-icon {
        width: 45px;
        height: 45px;
    }
}