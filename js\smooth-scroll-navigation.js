/**
 * INOVA Energy - Smooth Scroll Navigation System
 * Professional smooth scrolling between sections
 */

class SmoothScrollNavigation {
    constructor() {
        this.sections = [];
        this.currentSectionIndex = 0;
        this.isScrolling = false;
        this.scrollDuration = 1200;

        this.init();
    }

    init() {
        this.setupSections();
        this.setupScrollIndicators();
        this.setupEventListeners();
        console.log('Smooth Scroll Navigation initialized');
    }

    /**
     * Setup sections array
     */
    setupSections() {
        const sectionSelectors = [
            '#hero',
            '#about',
            '#services',
            '#projects',
            '#activities',
            '#contact'
        ];

        this.sections = sectionSelectors.map(selector => {
            const element = document.querySelector(selector);
            return element ? {
                element,
                id: selector.replace('#', ''),
                offsetTop: element.offsetTop
            } : null;
        }).filter(Boolean);

        console.log(`Found ${this.sections.length} sections`);
    }

    /**
     * Setup scroll indicators for each section
     */
    setupScrollIndicators() {
        this.sections.forEach((section, index) => {
            const indicator = this.createScrollIndicator(index);
            if (indicator) {
                section.element.appendChild(indicator);
            }
        });
    }

    /**
     * Create scroll indicator element
     */
    createScrollIndicator(sectionIndex) {
        const isLastSection = sectionIndex === this.sections.length - 1;

        const indicator = document.createElement('div');
        indicator.className = `scroll-indicator-ultimate ${isLastSection ? 'back-to-top' : 'next-section'}`;
        indicator.setAttribute('data-section-index', sectionIndex);
        indicator.setAttribute('data-animate', 'scroll-bounce');
        indicator.setAttribute('data-delay', '2.0s');

        const container = document.createElement('div');
        container.className = 'scroll-container';

        const text = document.createElement('div');
        text.className = 'scroll-text-ultimate';
        text.innerHTML = `<span>${isLastSection ? 'بازگشت به بالا' : 'ادامه مطالب'}</span>`;

        const arrow = document.createElement('div');
        arrow.className = 'scroll-arrow-ultimate';

        if (isLastSection) {
            arrow.innerHTML = `
                <div class="arrow-line"></div>
                <div class="arrow-head arrow-up"></div>
                <div class="arrow-glow"></div>
            `;
        } else {
            arrow.innerHTML = `
                <div class="arrow-line"></div>
                <div class="arrow-head"></div>
                <div class="arrow-glow"></div>
            `;
        }

        const pulse = document.createElement('div');
        pulse.className = 'scroll-pulse';

        container.appendChild(text);
        container.appendChild(arrow);
        container.appendChild(pulse);
        indicator.appendChild(container);

        return indicator;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Click events for scroll indicators
        document.addEventListener('click', (e) => {
            const indicator = e.target.closest('.scroll-indicator-ultimate');
            if (indicator) {
                e.preventDefault();
                const sectionIndex = parseInt(indicator.getAttribute('data-section-index'));
                this.handleScrollClick(sectionIndex);
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowDown' || e.key === 'PageDown') {
                e.preventDefault();
                this.scrollToNext();
            } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
                e.preventDefault();
                this.scrollToPrevious();
            } else if (e.key === 'Home') {
                e.preventDefault();
                this.scrollToSection(0);
            } else if (e.key === 'End') {
                e.preventDefault();
                this.scrollToSection(this.sections.length - 1);
            }
        });

        // Update current section on scroll
        window.addEventListener('scroll', this.throttle(() => {
            this.updateCurrentSection();
        }, 100));
    }

    /**
     * Handle scroll indicator click
     */
    handleScrollClick(sectionIndex) {
        const isLastSection = sectionIndex === this.sections.length - 1;

        if (isLastSection) {
            // Back to top
            this.scrollToSection(0);
        } else {
            // Next section
            this.scrollToSection(sectionIndex + 1);
        }
    }

    /**
     * Scroll to next section
     */
    scrollToNext() {
        if (this.currentSectionIndex < this.sections.length - 1) {
            this.scrollToSection(this.currentSectionIndex + 1);
        }
    }

    /**
     * Scroll to previous section
     */
    scrollToPrevious() {
        if (this.currentSectionIndex > 0) {
            this.scrollToSection(this.currentSectionIndex - 1);
        }
    }

    /**
     * Scroll to specific section with smooth animation
     */
    scrollToSection(index) {
        if (this.isScrolling || index < 0 || index >= this.sections.length) {
            return;
        }

        this.isScrolling = true;
        const targetSection = this.sections[index];
        const targetPosition = targetSection.offsetTop;

        // Add scroll animation class
        document.body.classList.add('smooth-scrolling');

        // Smooth scroll with easing
        this.smoothScrollTo(targetPosition, this.scrollDuration, () => {
            this.currentSectionIndex = index;
            this.isScrolling = false;
            document.body.classList.remove('smooth-scrolling');

            // Trigger section entrance animations
            this.triggerSectionAnimations(targetSection.element);
        });
    }

    /**
     * Smooth scroll implementation with easing
     */
    smoothScrollTo(targetPosition, duration, callback) {
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        const startTime = performance.now();

        const easeInOutCubic = (t) => {
            return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        };

        const animateScroll = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easedProgress = easeInOutCubic(progress);

            const currentPosition = startPosition + (distance * easedProgress);
            window.scrollTo(0, currentPosition);

            if (progress < 1) {
                requestAnimationFrame(animateScroll);
            } else {
                if (callback) callback();
            }
        };

        requestAnimationFrame(animateScroll);
    }

    /**
     * Update current section based on scroll position
     */
    updateCurrentSection() {
        if (this.isScrolling) return;

        const scrollPosition = window.pageYOffset + window.innerHeight / 2;

        for (let i = this.sections.length - 1; i >= 0; i--) {
            if (scrollPosition >= this.sections[i].offsetTop) {
                this.currentSectionIndex = i;
                break;
            }
        }
    }

    /**
     * Trigger section entrance animations
     */
    triggerSectionAnimations(sectionElement) {
        const animatedElements = sectionElement.querySelectorAll('[data-animate]');

        animatedElements.forEach((element, index) => {
            const delay = element.getAttribute('data-delay') || `${index * 0.1}s`;

            setTimeout(() => {
                element.classList.add('animate-in');
            }, parseFloat(delay) * 1000);
        });
    }

    /**
     * Throttle function for performance
     */
    throttle(func, limit) {
        let inThrottle;
        return function () {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Destroy navigation system
     */
    destroy() {
        // Remove event listeners and clean up
        document.removeEventListener('click', this.handleScrollClick);
        document.removeEventListener('keydown', this.handleKeydown);
        window.removeEventListener('scroll', this.updateCurrentSection);

        // Remove scroll indicators
        document.querySelectorAll('.scroll-indicator-ultimate').forEach(indicator => {
            indicator.remove();
        });

        console.log('Smooth Scroll Navigation destroyed');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new SmoothScrollNavigation();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmoothScrollNavigation;
}
