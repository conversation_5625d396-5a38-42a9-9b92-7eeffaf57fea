/**
 * Language Switcher for INOVA Energy Theme
 * Handles switching between Persian (RTL) and English (LTR) layouts
 */

(function ($) {
    'use strict';

    // Language switcher functionality
    window.toggleLanguage = function () {
        const currentLang = document.documentElement.lang === 'en' ? 'en' : 'fa';
        const newLang = currentLang === 'fa' ? 'en' : 'fa';

        // Show loading state
        const langButton = document.querySelector('.lang-toggle') || document.querySelector('.mobile-lang-toggle') || document.querySelector('.lang-switch');
        if (!langButton) {
            console.error('Language button not found');
            return;
        }

        const originalContent = langButton.innerHTML;
        langButton.innerHTML = '<div class="loading-spinner"></div>';
        langButton.disabled = true;

        // Add transition class for smooth animation
        document.body.classList.add('transitioning');

        // Update immediately without AJAX
        setTimeout(() => {
            // Update body classes
            document.body.classList.remove('lang-fa', 'lang-en', 'rtl', 'ltr');
            document.body.classList.add('lang-' + newLang);
            document.body.classList.add(newLang === 'fa' ? 'rtl' : 'ltr');

            // Update HTML direction and language
            document.documentElement.dir = newLang === 'fa' ? 'rtl' : 'ltr';
            document.documentElement.lang = newLang === 'fa' ? 'fa-IR' : 'en-US';

            // Update content immediately
            updateContent(newLang);

            // Update button text
            updateLanguageButton(newLang);

            // Store language preference
            localStorage.setItem('inova_language', newLang);

            // Restore button state
            langButton.disabled = false;

            // Remove transition class
            setTimeout(() => {
                document.body.classList.remove('transitioning');
            }, 300);

            // Trigger custom event
            if (typeof $ !== 'undefined') {
                $(document).trigger('languageChanged', [newLang]);
            } else {
                document.dispatchEvent(new CustomEvent('languageChanged', { detail: newLang }));
            }

            console.log('Language switched to:', newLang);
        }, 500);
    };

    // Update language button text
    function updateLanguageButton(lang) {
        const langButton = document.querySelector('.lang-switch .lang-text');
        if (langButton) {
            langButton.textContent = lang === 'fa' ? 'EN' : 'فا';
        }
    }

    // Update content based on language
    function updateContent(lang) {
        // Update page title
        const pageTitle = lang === 'fa' ?
            'INOVA Energy | پیشگام در صنعت انرژی پایدار' :
            'INOVA Energy | Leading Sustainable Energy Innovation';
        document.title = pageTitle;

        // Update meta description
        const metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc) {
            const description = lang === 'fa' ?
                'گروه بین‌المللی INOVA Energy پیشگام در صنعت انرژی پایدار، پتروشیمی و فناوری‌های نوین' :
                'INOVA Energy international group, leading in sustainable energy, petrochemicals and innovative technologies';
            metaDesc.setAttribute('content', description);
        }

        // Update language attributes
        document.documentElement.setAttribute('lang', lang === 'fa' ? 'fa-IR' : 'en-US');

        // Update fonts
        const fontLink = document.querySelector('link[href*="fonts.googleapis.com"]');
        if (fontLink) {
            if (lang === 'fa') {
                fontLink.href = 'https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700;800&display=swap';
            } else {
                fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap';
            }
        }

        // Update navigation menu
        updateNavigationMenu(lang);

        // Update dynamic content
        updateDynamicContent(lang);

        // Update form placeholders
        updateFormPlaceholders(lang);

        // Update aria-labels
        updateAriaLabels(lang);
    }

    // Update navigation menu
    function updateNavigationMenu(lang) {
        const navItems = {
            fa: [
                { href: '#hero', text: 'خانه' },
                { href: '#about', text: 'درباره ما' },
                { href: '#rebranding', text: 'ریبرندینگ' },
                { href: '#activities', text: 'فعالیت‌ها' },
                { href: '#services', text: 'خدمات' },
                { href: '#contact', text: 'تماس' }
            ],
            en: [
                { href: '#hero', text: 'Home' },
                { href: '#about', text: 'About' },
                { href: '#rebranding', text: 'Rebranding' },
                { href: '#activities', text: 'Activities' },
                { href: '#services', text: 'Services' },
                { href: '#contact', text: 'Contact' }
            ]
        };

        // Update main navigation
        const navLinks = document.querySelectorAll('.nav-menu a, .mobile-nav-menu a');
        const currentItems = navItems[lang];

        navLinks.forEach((link, index) => {
            if (currentItems[index]) {
                link.textContent = currentItems[index].text;
            }
        });

        // Update language button text
        const langButtons = document.querySelectorAll('.lang-text, .mobile-lang-text');
        langButtons.forEach(button => {
            button.textContent = lang === 'fa' ? 'EN' : 'فا';
        });
    }

    // Comprehensive translation data
    const translations = {
        fa: {
            // Navigation
            'home': 'خانه',
            'about': 'درباره ما',
            'rebranding': 'ریبرندینگ',
            'activities': 'فعالیت‌ها',
            'services': 'خدمات',
            'contact': 'تماس',

            // Hero Section
            'since_badge': 'فعال از سال 1999',
            'hero_title_1': 'INOVA',
            'hero_title_2': 'Energy',
            'hero_title_3': 'پیشگام انرژی پایدار',
            'hero_description': 'گروه بین‌المللی سرمایه‌گذاری و توسعه در حوزه انرژی با بیش از 25 سال تجربه در صنایع نفت، گاز، پتروشیمی و انرژی‌های تجدیدپذیر',
            'explore_activities': 'کشف فعالیت‌ها',
            'contact_us': 'تماس با ما',
            'years_experience': 'سال تجربه',
            'main_activities': 'محور فعالیت',
            'established': 'تأسیس',
            'scroll_down': 'اسکرول کنید',

            // Services Section
            'services_badge': 'خدمات ما',
            'services_title_1': 'خدمات',
            'services_title_2': 'تخصصی',
            'services_title_3': 'INOVA Energy',
            'services_description': 'ما طیف گسترده‌ای از خدمات تخصصی در حوزه انرژی، مهندسی و مشاوره ارائه می‌دهیم',
            'engineering_consulting': 'مشاوره مهندسی',
            'engineering_consulting_desc': 'ارائه خدمات مشاوره تخصصی در طراحی، اجرا و بهینه‌سازی پروژه‌های صنعتی و انرژی',
            'basic_engineering': 'مهندسی پایه',
            'detailed_engineering': 'مهندسی تفصیلی',
            'project_management': 'مدیریت پروژه',
            'equipment_manufacturing': 'ساخت تجهیزات',
            'equipment_manufacturing_desc': 'طراحی و ساخت تجهیزات صنعتی شامل مخازن، تاورها، مبدل‌ها و سایر تجهیزات فرایندی',
            'pressure_vessels': 'مخازن تحت فشار',
            'heat_exchangers': 'مبدل‌های حرارتی',
            'distillation_towers': 'برج‌های تقطیر',
            'project_implementation': 'اجرای پروژه',
            'project_implementation_desc': 'اجرای کامل پروژه‌های صنعتی از مرحله طراحی تا راه‌اندازی و تحویل نهایی',
            'epc_services': 'خدمات EPC',
            'commissioning': 'راه‌اندازی',
            'maintenance': 'نگهداری',
            'supply_chain': 'مدیریت زنجیره تأمین',
            'supply_chain_desc': 'مدیریت کامل زنجیره تأمین مواد شیمیایی و پلیمری با شبکه جهانی دفاتر تجاری',
            'global_sourcing': 'تأمین جهانی',
            'logistics': 'لجستیک',
            'quality_control': 'کنترل کیفیت',
            'learn_more': 'اطلاعات بیشتر',
            'services_cta_title': 'نیاز به مشاوره دارید؟',
            'services_cta_desc': 'تیم متخصص ما آماده ارائه بهترین راه‌حل‌ها برای پروژه شما است.',
            'get_consultation': 'دریافت مشاوره',

            // Common
            'company-name': 'اینووا انرژی',
            'company-tagline': 'راهکارهای انرژی پایدار',
            'cta-button': 'شروع پروژه',
            'lang-switch-text': 'EN'
        },
        en: {
            // Navigation
            'home': 'Home',
            'about': 'About',
            'rebranding': 'Rebranding',
            'activities': 'Activities',
            'services': 'Services',
            'contact': 'Contact',

            // Hero Section
            'since_badge': 'Since 1999',
            'hero_title_1': 'INOVA',
            'hero_title_2': 'Energy',
            'hero_title_3': 'Sustainable Energy Pioneer',
            'hero_description': 'International investment and development group in energy sector with over 25 years of experience in oil, gas, petrochemical and renewable energy industries',
            'explore_activities': 'Explore Activities',
            'contact_us': 'Contact Us',
            'years_experience': 'Years Experience',
            'main_activities': 'Main Activities',
            'established': 'Established',
            'scroll_down': 'Scroll Down',

            // Services Section
            'services_badge': 'Our Services',
            'services_title_1': 'Services',
            'services_title_2': 'Specialized',
            'services_title_3': 'INOVA Energy',
            'services_description': 'We provide a wide range of specialized services in energy, engineering and consulting',
            'engineering_consulting': 'Engineering Consulting',
            'engineering_consulting_desc': 'Providing specialized consulting services in design, implementation and optimization of industrial and energy projects',
            'basic_engineering': 'Basic Engineering',
            'detailed_engineering': 'Detailed Engineering',
            'project_management': 'Project Management',
            'equipment_manufacturing': 'Equipment Manufacturing',
            'equipment_manufacturing_desc': 'Design and manufacturing of industrial equipment including tanks, towers, exchangers and other process equipment',
            'pressure_vessels': 'Pressure Vessels',
            'heat_exchangers': 'Heat Exchangers',
            'distillation_towers': 'Distillation Towers',
            'project_implementation': 'Project Implementation',
            'project_implementation_desc': 'Complete implementation of industrial projects from design stage to commissioning and final delivery',
            'epc_services': 'EPC Services',
            'commissioning': 'Commissioning',
            'maintenance': 'Maintenance',
            'supply_chain': 'Supply Chain Management',
            'supply_chain_desc': 'Complete management of chemical and polymer supply chain with global network of commercial offices',
            'global_sourcing': 'Global Sourcing',
            'logistics': 'Logistics',
            'quality_control': 'Quality Control',
            'learn_more': 'Learn More',
            'services_cta_title': 'Need Consultation?',
            'services_cta_desc': 'Our expert team is ready to provide the best solutions for your project.',
            'get_consultation': 'Get Consultation',

            // Common
            'company-name': 'INOVA Energy',
            'company-tagline': 'Sustainable Energy Solutions',
            'cta-button': 'Start Project',
            'lang-switch-text': 'فا'
        }
    };

    // Update dynamic content
    function updateDynamicContent(lang) {
        const currentTranslations = translations[lang];

        // Update all elements with translation keys
        Object.keys(currentTranslations).forEach(key => {
            const elements = document.querySelectorAll(`[data-translate="${key}"], .${key}`);
            elements.forEach(element => {
                element.textContent = currentTranslations[key];
            });
        });

        // Update elements with inova_translate function calls (PHP generated content)
        updatePHPTranslations(lang);
    }

    // Update PHP generated translations
    function updatePHPTranslations(lang) {
        const currentTranslations = translations[lang];

        // Find all elements that might contain PHP translations
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
            if (element.children.length === 0) { // Only text nodes
                const text = element.textContent.trim();
                // Check if this text matches any of our translation values
                Object.keys(currentTranslations).forEach(key => {
                    const faText = translations.fa[key];
                    const enText = translations.en[key];
                    if (text === faText || text === enText) {
                        element.textContent = currentTranslations[key];
                    }
                });
            }
        });
    }

    // Update form placeholders
    function updateFormPlaceholders(lang) {
        const placeholders = {
            fa: {
                'firstName': 'نام',
                'lastName': 'نام خانوادگی',
                'email': 'ایمیل',
                'phone': 'تلفن',
                'company': 'شرکت',
                'message': 'پیام خود را اینجا بنویسید...'
            },
            en: {
                'firstName': 'First Name',
                'lastName': 'Last Name',
                'email': 'Email',
                'phone': 'Phone',
                'company': 'Company',
                'message': 'Write your message here...'
            }
        };

        Object.keys(placeholders[lang]).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.placeholder = placeholders[lang][id];
            }
        });
    }

    // Update aria-labels
    function updateAriaLabels(lang) {
        const ariaLabels = {
            fa: {
                'lang-switch': 'تغییر زبان',
                'menu-toggle': 'منو',
                'social-linkedin': 'لینکدین',
                'social-twitter': 'توییتر',
                'social-instagram': 'اینستاگرام'
            },
            en: {
                'lang-switch': 'Change Language',
                'menu-toggle': 'Menu',
                'social-linkedin': 'LinkedIn',
                'social-twitter': 'Twitter',
                'social-instagram': 'Instagram'
            }
        };

        Object.keys(ariaLabels[lang]).forEach(className => {
            const elements = document.querySelectorAll(`[aria-label][class*="${className}"]`);
            elements.forEach(element => {
                element.setAttribute('aria-label', ariaLabels[lang][className]);
            });
        });
    }

    // Initialize language on page load
    function initializeLanguage() {
        // Check for saved language preference
        const savedLang = localStorage.getItem('inova_language');
        const htmlLang = document.documentElement.lang;
        const currentLang = savedLang || (htmlLang === 'en-US' || htmlLang === 'en' ? 'en' : 'fa');

        // Apply the language immediately
        document.body.classList.remove('lang-fa', 'lang-en', 'rtl', 'ltr');
        document.body.classList.add('lang-' + currentLang);
        document.body.classList.add(currentLang === 'fa' ? 'rtl' : 'ltr');

        document.documentElement.dir = currentLang === 'fa' ? 'rtl' : 'ltr';
        document.documentElement.lang = currentLang === 'fa' ? 'fa-IR' : 'en-US';

        updateContent(currentLang);
        updateLanguageButton(currentLang);

        console.log('Language initialized:', currentLang);
    }

    // Initialize language switcher
    if (typeof $ !== 'undefined') {
        $(document).ready(function () {
            // Initialize language
            initializeLanguage();

            // Add loading spinner styles
            if (!document.getElementById('language-switcher-styles')) {
                const styles = document.createElement('style');
                styles.id = 'language-switcher-styles';
                styles.textContent = `
                    .loading-spinner {
                        width: 16px;
                        height: 16px;
                        border: 2px solid rgba(255,255,255,0.3);
                        border-top: 2px solid var(--accent);
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }

                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }

                    .lang-switch:disabled {
                        opacity: 0.6;
                        cursor: not-allowed;
                    }
                `;
                document.head.appendChild(styles);
            }

            // Add click handlers for language buttons
            $(document).on('click', '.lang-toggle, .mobile-lang-toggle, .lang-switch', function (e) {
                e.preventDefault();
                toggleLanguage();
            });

            // Handle language change animations
            $(document).on('languageChanged', function (event, newLang) {
                // Animate direction change
                $('body').addClass('transitioning');

                setTimeout(() => {
                    $('body').removeClass('transitioning');
                }, 300);
            });

            // Update arrows direction based on language
            function updateArrowDirections() {
                const isRTL = document.body.classList.contains('rtl');
                const arrows = document.querySelectorAll('.btn-arrow path');

                arrows.forEach(arrow => {
                    if (isRTL) {
                        arrow.setAttribute('d', 'M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z');
                    } else {
                        arrow.setAttribute('d', 'M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z');
                    }
                });
            }

            // Initialize arrow directions
            updateArrowDirections();

            // Update arrows on language change
            $(document).on('languageChanged', updateArrowDirections);
        });
    } else {
        // Vanilla JS fallback
        document.addEventListener('DOMContentLoaded', function () {
            initializeLanguage();

            // Add click handlers for language buttons
            document.addEventListener('click', function (e) {
                if (e.target.matches('.lang-toggle, .mobile-lang-toggle, .lang-switch') ||
                    e.target.closest('.lang-toggle, .mobile-lang-toggle, .lang-switch')) {
                    e.preventDefault();
                    toggleLanguage();
                }
            });
        });
    }

})(jQuery);
