/* ===================================
   COMPREHENSIVE RTL/LTR LAYOUT SYSTEM
   Advanced bidirectional support
   =================================== */

/* Global RTL/LTR Base Styles */
.rtl {
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}

/* Language-specific font families */
.lang-fa {
    font-family: 'Vazirmatn', 'Tahoma', sans-serif;
}

.lang-en {
    font-family: 'Inter', 'Arial', sans-serif;
}

/* Smooth transitions for language switching */
body.transitioning * {
    transition: all 0.3s ease !important;
}

/* ===================================
   HEADER RTL/LTR STYLES
   =================================== */

/* Header Container Layout */
.rtl .site-header .header-container {
    flex-direction: row-reverse;
}

.ltr .site-header .header-container {
    flex-direction: row;
}

/* Navigation Menus */
.rtl .nav-menu,
.rtl .mobile-nav-menu {
    padding-right: 0;
    padding-left: 0;
}

.ltr .nav-menu,
.ltr .mobile-nav-menu {
    padding-left: 0;
    padding-right: 0;
}

/* Site Branding */
.rtl .site-branding {
    margin-right: 0;
    margin-left: 2rem;
}

.ltr .site-branding {
    margin-left: 0;
    margin-right: 2rem;
}

/* Language Switcher Position */
.rtl .language-switcher {
    margin-right: auto;
    margin-left: 0;
}

.ltr .language-switcher {
    margin-left: auto;
    margin-right: 0;
}

/* Mobile Menu Toggle */
.rtl .mobile-menu-toggle {
    margin-right: auto;
    margin-left: 0;
}

.ltr .mobile-menu-toggle {
    margin-left: auto;
    margin-right: 0;
}

/* ===================================
   HERO SECTION RTL/LTR STYLES
   =================================== */

/* Hero Section Base */
.rtl .hero-section,
.rtl .hero-section-professional {
    direction: rtl;
    text-align: right;
}

.ltr .hero-section,
.ltr .hero-section-professional {
    direction: ltr;
    text-align: left;
}

/* Hero Content Alignment */
.rtl .hero-content,
.rtl .hero-content-main {
    align-items: flex-start;
    text-align: right;
}

.ltr .hero-content,
.ltr .hero-content-main {
    align-items: flex-start;
    text-align: left;
}

/* Hero Stats Layout */
.rtl .hero-stats,
.rtl .hero-stats-section .stats-grid {
    flex-direction: row-reverse;
}

.ltr .hero-stats,
.ltr .hero-stats-section .stats-grid {
    flex-direction: row;
}

/* Hero CTA Buttons */
.rtl .hero-cta,
.rtl .hero-cta-section .cta-buttons-container {
    flex-direction: row-reverse;
    gap: 1rem;
}

.ltr .hero-cta,
.ltr .hero-cta-section .cta-buttons-container {
    flex-direction: row;
    gap: 1rem;
}

/* Button Icons Direction */
.rtl .cta-primary-advanced:hover .button-icon svg,
.rtl .cta-secondary-advanced:hover .button-icon svg {
    transform: translateX(-4px);
}

.ltr .cta-primary-advanced:hover .button-icon svg,
.ltr .cta-secondary-advanced:hover .button-icon svg {
    transform: translateX(4px);
}

/* About Section RTL Styles */
.rtl .about-section {
    direction: rtl;
    text-align: right;
}

.rtl .about-grid {
    direction: rtl;
}

.rtl .about-card {
    text-align: right;
}

/* Rebranding Section RTL Styles */
.rtl .rebranding-section {
    direction: rtl;
    text-align: right;
}

.rtl .rebranding-grid {
    direction: rtl;
}

.rtl .rebranding-feature {
    text-align: right;
}

/* Activities Section RTL Styles */
.rtl .activities-section {
    direction: rtl;
    text-align: right;
}

.rtl .activities-grid {
    direction: rtl;
}

.rtl .activity-card {
    text-align: right;
}

/* Services Section RTL Styles */
.rtl .services-section {
    direction: rtl;
    text-align: right;
}

.rtl .services-grid {
    direction: rtl;
}

.rtl .service-card {
    text-align: right;
}

.rtl .service-features li {
    padding-right: 1.5rem;
    padding-left: 0;
}

.rtl .service-features li:before {
    right: 0;
    left: auto;
}

/* Projects Section RTL Styles */
.rtl .projects-section {
    direction: rtl;
    text-align: right;
}

.rtl .projects-grid {
    direction: rtl;
}

.rtl .project-card {
    text-align: right;
}

/* Contact Section RTL Styles */
.rtl .contact-section {
    direction: rtl;
    text-align: right;
}

.rtl .contact-grid {
    direction: rtl;
}

.rtl .contact-form {
    text-align: right;
}

.rtl .contact-info {
    text-align: right;
}

/* Footer RTL Styles */
.rtl .site-footer {
    direction: rtl;
    text-align: right;
}

.rtl .footer-grid {
    direction: rtl;
}

.rtl .footer-widget {
    text-align: right;
}

/* Common Components RTL Styles */
.rtl .section-title,
.rtl .section-subtitle {
    text-align: right;
}

.rtl .feature-card,
.rtl .info-card {
    text-align: right;
}

/* Animation Adjustments for RTL */
.rtl [data-aos="fade-left"] {
    transform: translate(-100px, 0);
}

.rtl [data-aos="fade-right"] {
    transform: translate(100px, 0);
}

/* Language Switcher RTL Position */
.rtl .language-switcher {
    margin-right: auto;
    margin-left: 0;
}

/* Mobile Menu RTL Styles */
.rtl .mobile-menu-toggle {
    margin-right: auto;
    margin-left: 0;
}

.rtl .mobile-navigation {
    right: 0;
    left: auto;
    transform: translateX(100%);
}

.rtl .mobile-navigation.active {
    transform: translateX(0);
}