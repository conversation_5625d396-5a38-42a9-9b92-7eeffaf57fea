/* ===================================
   SINGLE VIEWPORT LAYOUT SYSTEM
   Ensures each section fits perfectly in one viewport
   =================================== */

/* Global Viewport Constraints */
.hero-section-professional,
.about-section-ultimate,
.services-section-professional,
.projects-section-professional,
.activities-section-fixed,
.rebranding-section-ultimate,
.contact-section-fixed {
    min-height: 100vh !important;
    max-height: 100vh !important;
    height: 100vh !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    position: relative !important;
    box-sizing: border-box !important;
}

/* Container Optimization */
.hero-container-professional,
.about-container,
.services-container-professional,
.projects-container-professional,
.activities-container,
.rebranding-container,
.contact-container {
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
    max-height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    padding: clamp(20px, 3vh, 40px) clamp(20px, 5vw, 80px) !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

/* Content Scaling System */
.section-header,
.hero-title-section,
.about-header,
.services-header,
.projects-header,
.activities-header,
.rebranding-header,
.contact-header {
    flex-shrink: 0 !important;
    margin-bottom: clamp(20px, 3vh, 40px) !important;
    text-align: center !important;
}

.section-content,
.hero-content-main,
.about-content,
.services-content,
.projects-content,
.activities-content,
.rebranding-content,
.contact-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
}

/* Grid Optimization for Single Viewport */
.services-grid-professional,
.projects-grid-professional,
.activities-grid,
.about-content-grid,
.rebranding-content-grid {
    width: 100% !important;
    max-width: 100% !important;
    height: auto !important;
    max-height: 60vh !important;
    overflow: hidden !important;
    gap: clamp(15px, 2vh, 25px) !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Card Height Optimization */
.service-card-professional,
.project-card-professional,
.activity-card,
.story-card,
.values-card {
    height: auto !important;
    max-height: clamp(200px, 25vh, 300px) !important;
    min-height: clamp(180px, 20vh, 250px) !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    padding: clamp(15px, 2vh, 25px) !important;
    box-sizing: border-box !important;
}

/* Typography Scaling for Viewport */
.section-title-professional,
.section-title,
.hero-title-professional {
    font-size: clamp(1.5rem, 4vh, 3rem) !important;
    line-height: 1.1 !important;
    margin: 0 0 clamp(10px, 2vh, 20px) 0 !important;
}

.section-description-professional,
.section-description,
.hero-description-professional {
    font-size: clamp(0.9rem, 2vh, 1.2rem) !important;
    line-height: 1.4 !important;
    margin: 0 0 clamp(15px, 3vh, 25px) 0 !important;
    max-height: clamp(60px, 8vh, 100px) !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Button Optimization */
.cta-buttons-container {
    flex-shrink: 0 !important;
    margin: clamp(15px, 3vh, 25px) 0 0 0 !important;
    gap: clamp(10px, 2vh, 20px) !important;
}

.cta-primary-advanced,
.cta-secondary-advanced {
    padding: clamp(10px, 1.5vh, 15px) clamp(20px, 3vw, 30px) !important;
    font-size: clamp(0.9rem, 1.8vh, 1.1rem) !important;
}

/* Stats Grid Optimization */
.stats-grid {
    max-height: clamp(80px, 12vh, 120px) !important;
    margin: clamp(15px, 3vh, 25px) 0 !important;
}

.stat-item {
    padding: clamp(10px, 1.5vh, 15px) !important;
}

.stat-number {
    font-size: clamp(1.2rem, 3vh, 2rem) !important;
    line-height: 1 !important;
}

.stat-label {
    font-size: clamp(0.8rem, 1.5vh, 1rem) !important;
    line-height: 1.2 !important;
}

/* Desktop Large (1920px+) */
@media (min-width: 1920px) {
    .services-grid-professional,
    .projects-grid-professional {
        grid-template-columns: repeat(4, 1fr) !important;
        max-height: 50vh !important;
    }
    
    .activities-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        max-height: 55vh !important;
    }
}

/* Desktop Standard (1200px - 1919px) */
@media (min-width: 1200px) and (max-width: 1919px) {
    .services-grid-professional,
    .projects-grid-professional {
        grid-template-columns: repeat(3, 1fr) !important;
        max-height: 55vh !important;
    }
    
    .activities-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        max-height: 60vh !important;
    }
}

/* Desktop Small (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        max-height: 65vh !important;
    }
}

/* Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        max-height: 60vh !important;
    }
    
    .service-card-professional,
    .project-card-professional,
    .activity-card {
        max-height: clamp(180px, 22vh, 250px) !important;
        min-height: clamp(160px, 18vh, 220px) !important;
    }
}

/* Mobile Large (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid {
        grid-template-columns: 1fr !important;
        max-height: 65vh !important;
        overflow-y: auto !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
    }
    
    .services-grid-professional::-webkit-scrollbar,
    .projects-grid-professional::-webkit-scrollbar,
    .activities-grid::-webkit-scrollbar {
        display: none !important;
    }
    
    .service-card-professional,
    .project-card-professional,
    .activity-card {
        max-height: clamp(150px, 20vh, 200px) !important;
        min-height: clamp(130px, 16vh, 180px) !important;
    }
}

/* Mobile Small (320px - 575px) */
@media (max-width: 575px) {
    .hero-section-professional,
    .about-section-ultimate,
    .services-section-professional,
    .projects-section-professional,
    .activities-section-fixed,
    .rebranding-section-ultimate,
    .contact-section-fixed {
        padding: 0 !important;
    }
    
    .hero-container-professional,
    .about-container,
    .services-container-professional,
    .projects-container-professional,
    .activities-container,
    .rebranding-container,
    .contact-container {
        padding: clamp(80px, 12vh, 100px) 15px clamp(20px, 3vh, 30px) 15px !important;
    }
    
    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid {
        grid-template-columns: 1fr !important;
        max-height: 60vh !important;
        overflow-y: auto !important;
        gap: 15px !important;
    }
    
    .service-card-professional,
    .project-card-professional,
    .activity-card {
        max-height: clamp(140px, 18vh, 180px) !important;
        min-height: clamp(120px, 15vh, 160px) !important;
        padding: 15px !important;
    }
    
    .stats-grid {
        grid-template-columns: 1fr !important;
        max-height: clamp(120px, 15vh, 150px) !important;
        gap: 10px !important;
    }
    
    .cta-buttons-container {
        flex-direction: column !important;
        gap: 10px !important;
    }
}

/* Very Short Screens */
@media (max-height: 600px) {
    .hero-section-professional,
    .about-section-ultimate,
    .services-section-professional,
    .projects-section-professional,
    .activities-section-fixed,
    .rebranding-section-ultimate,
    .contact-section-fixed {
        min-height: 100vh !important;
        max-height: none !important;
        height: auto !important;
        overflow: visible !important;
    }
    
    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid {
        max-height: 400px !important;
        overflow-y: auto !important;
    }
}
