/* ===================================
   HERO LOGO ULTIMATE DESIGN
   Professional logo animations with energy effects
   =================================== */

/* ===================================
   LOGO SECTION BASE
   =================================== */

.hero-logo-ultimate {
    position: relative;
    z-index: 10;
    margin-bottom: clamp(40px, 8vh, 80px);
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo-container-ultimate {
    position: relative;
    width: clamp(120px, 20vw, 200px);
    height: clamp(120px, 20vw, 200px);
    display: flex;
    justify-content: center;
    align-items: center;
}

/* ===================================
   LOGO BACKGROUND EFFECTS
   =================================== */

.logo-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.logo-glow-primary {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150%;
    height: 150%;
    background: radial-gradient(
        circle,
        rgba(0, 255, 209, 0.3) 0%,
        rgba(0, 255, 209, 0.1) 40%,
        transparent 70%
    );
    border-radius: 50%;
    animation: logoGlowPrimary 4s ease-in-out infinite;
}

.logo-glow-secondary {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle,
        rgba(0, 163, 255, 0.2) 0%,
        rgba(0, 163, 255, 0.05) 50%,
        transparent 80%
    );
    border-radius: 50%;
    animation: logoGlowSecondary 6s ease-in-out infinite;
}

.logo-shadow-depth {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 80%;
    background: radial-gradient(
        circle,
        rgba(0, 0, 0, 0.4) 0%,
        rgba(0, 0, 0, 0.2) 50%,
        transparent 100%
    );
    border-radius: 50%;
    filter: blur(20px);
}

/* ===================================
   ANIMATED LOGO RINGS
   =================================== */

.logo-rings-ultimate {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.logo-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    border: 2px solid transparent;
    transform: translate(-50%, -50%);
}

.logo-ring.ring-outer {
    width: 100%;
    height: 100%;
    border-color: rgba(0, 255, 209, 0.6);
    border-style: dashed;
    border-width: 2px;
    animation: logoRingOuter 20s linear infinite;
}

.logo-ring.ring-middle {
    width: 80%;
    height: 80%;
    border-color: rgba(0, 163, 255, 0.5);
    border-style: solid;
    border-width: 1px;
    animation: logoRingMiddle 15s linear infinite reverse;
}

.logo-ring.ring-inner {
    width: 60%;
    height: 60%;
    border-color: rgba(138, 43, 226, 0.4);
    border-style: dotted;
    border-width: 2px;
    animation: logoRingInner 10s linear infinite;
}

/* ===================================
   MAIN LOGO
   =================================== */

.logo-main-container {
    position: relative;
    z-index: 5;
    width: 70%;
    height: 70%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hero-logo-ultimate-main {
    width: 80%;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 0 20px rgba(0, 255, 209, 0.5));
    animation: logoMain 3s ease-in-out infinite;
}

/* ===================================
   LOGO PULSE EFFECTS
   =================================== */

.logo-pulse-ultimate {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
}

.pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(0, 255, 209, 0.8);
    border-radius: 50%;
    animation: logoPulseExpand 3s ease-out infinite;
}

.pulse-ring.pulse-1 {
    animation-delay: 0s;
}

.pulse-ring.pulse-2 {
    animation-delay: 1s;
}

.pulse-ring.pulse-3 {
    animation-delay: 2s;
}

/* ===================================
   LOGO ANIMATIONS
   =================================== */

@keyframes logoGlowPrimary {
    0%, 100% { 
        opacity: 0.3; 
        transform: translate(-50%, -50%) scale(1); 
    }
    50% { 
        opacity: 0.6; 
        transform: translate(-50%, -50%) scale(1.1); 
    }
}

@keyframes logoGlowSecondary {
    0%, 100% { 
        opacity: 0.2; 
        transform: translate(-50%, -50%) scale(1) rotate(0deg); 
    }
    50% { 
        opacity: 0.4; 
        transform: translate(-50%, -50%) scale(1.05) rotate(180deg); 
    }
}

@keyframes logoRingOuter {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes logoRingMiddle {
    0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
    50% { transform: translate(-50%, -50%) rotate(180deg) scale(1.05); }
    100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

@keyframes logoRingInner {
    0% { 
        transform: translate(-50%, -50%) rotate(0deg); 
        opacity: 0.4; 
    }
    25% { 
        transform: translate(-50%, -50%) rotate(90deg); 
        opacity: 0.8; 
    }
    50% { 
        transform: translate(-50%, -50%) rotate(180deg); 
        opacity: 0.6; 
    }
    75% { 
        transform: translate(-50%, -50%) rotate(270deg); 
        opacity: 0.8; 
    }
    100% { 
        transform: translate(-50%, -50%) rotate(360deg); 
        opacity: 0.4; 
    }
}

@keyframes logoMain {
    0%, 100% { 
        transform: scale(1); 
        filter: drop-shadow(0 0 20px rgba(0, 255, 209, 0.5)); 
    }
    50% { 
        transform: scale(1.05); 
        filter: drop-shadow(0 0 30px rgba(0, 255, 209, 0.8)); 
    }
}

@keyframes logoPulseExpand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
        border-width: 3px;
    }
    100% {
        width: 150%;
        height: 150%;
        opacity: 0;
        border-width: 0;
    }
}

/* ===================================
   LOGO ENTRANCE ANIMATION
   =================================== */

.hero-logo-ultimate[data-animate="logo-entrance"] {
    animation: logoEntrance 2s ease-out forwards;
}

@keyframes logoEntrance {
    0% {
        opacity: 0;
        transform: scale(0.5) translateY(50px);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1) translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* ===================================
   RESPONSIVE LOGO DESIGN
   =================================== */

@media (max-width: 768px) {
    .logo-container-ultimate {
        width: clamp(100px, 25vw, 150px);
        height: clamp(100px, 25vw, 150px);
    }
    
    .logo-ring.ring-outer {
        border-width: 1px;
    }
    
    .logo-ring.ring-middle {
        border-width: 1px;
    }
    
    .logo-ring.ring-inner {
        border-width: 1px;
    }
    
    .hero-logo-ultimate-main {
        filter: drop-shadow(0 0 15px rgba(0, 255, 209, 0.5));
    }
}

@media (max-width: 480px) {
    .logo-container-ultimate {
        width: clamp(80px, 30vw, 120px);
        height: clamp(80px, 30vw, 120px);
    }
    
    .hero-logo-ultimate {
        margin-bottom: clamp(30px, 6vh, 50px);
    }
}
