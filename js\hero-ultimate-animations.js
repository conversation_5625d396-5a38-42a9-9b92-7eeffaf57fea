/**
 * INOVA Energy Hero Section - Ultimate Professional Animations
 * Advanced JavaScript animations and interactions
 */

class HeroUltimateAnimations {
    constructor() {
        this.heroSection = document.querySelector('.hero-ultimate-professional');
        this.isInitialized = false;
        this.animationFrameId = null;
        this.observers = [];
        this.counters = [];
        
        this.init();
    }

    init() {
        if (!this.heroSection || this.isInitialized) return;
        
        this.setupIntersectionObserver();
        this.initializeCounterAnimations();
        this.initializeParallaxEffects();
        this.initializeMouseInteractions();
        this.initializeKeyboardNavigation();
        this.initializePerformanceOptimizations();
        
        this.isInitialized = true;
        console.log('Hero Ultimate Animations initialized');
    }

    /**
     * Setup Intersection Observer for entrance animations
     */
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -10% 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerEntranceAnimations();
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        observer.observe(this.heroSection);
        this.observers.push(observer);
    }

    /**
     * Trigger all entrance animations in sequence
     */
    triggerEntranceAnimations() {
        const animationSequence = [
            { element: '[data-animate="logo-entrance"]', delay: 200 },
            { element: '[data-animate="title-cascade"]', delay: 400 },
            { element: '[data-animate="description-fade-in"]', delay: 800 },
            { element: '[data-animate="stats-counter"]', delay: 1000 },
            { element: '[data-animate="cta-entrance"]', delay: 1800 },
            { element: '[data-animate="scroll-bounce"]', delay: 2000 }
        ];

        animationSequence.forEach(({ element, delay }) => {
            setTimeout(() => {
                const el = this.heroSection.querySelector(element);
                if (el) {
                    el.classList.add('animate-in');
                    this.triggerSpecificAnimation(el);
                }
            }, delay);
        });
    }

    /**
     * Trigger specific animations based on element type
     */
    triggerSpecificAnimation(element) {
        const animationType = element.getAttribute('data-animate');
        
        switch (animationType) {
            case 'stats-counter':
                this.animateCounters(element);
                break;
            case 'scroll-bounce':
                this.animateScrollIndicator(element);
                break;
            default:
                break;
        }
    }

    /**
     * Initialize and animate counter numbers
     */
    initializeCounterAnimations() {
        const counterElements = this.heroSection.querySelectorAll('[data-count]');
        
        counterElements.forEach(element => {
            const targetValue = parseInt(element.getAttribute('data-count'));
            const counter = {
                element,
                targetValue,
                currentValue: 0,
                increment: targetValue / 60, // 60 frames for 1 second at 60fps
                isAnimating: false
            };
            
            this.counters.push(counter);
        });
    }

    /**
     * Animate counter numbers with easing
     */
    animateCounters(statsContainer) {
        this.counters.forEach(counter => {
            if (counter.isAnimating) return;
            
            counter.isAnimating = true;
            const startTime = performance.now();
            const duration = 2000; // 2 seconds

            const animate = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Easing function (ease-out)
                const easeOut = 1 - Math.pow(1 - progress, 3);
                
                counter.currentValue = counter.targetValue * easeOut;
                counter.element.textContent = Math.floor(counter.currentValue);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    counter.element.textContent = counter.targetValue;
                    counter.isAnimating = false;
                }
            };
            
            requestAnimationFrame(animate);
        });
    }

    /**
     * Initialize parallax effects for background elements
     */
    initializeParallaxEffects() {
        const parallaxElements = [
            { selector: '.energy-ring', speed: 0.5 },
            { selector: '.floating-element-ultimate', speed: 0.3 },
            { selector: '.particle-layer', speed: 0.2 }
        ];

        const handleScroll = () => {
            const scrollY = window.pageYOffset;
            
            parallaxElements.forEach(({ selector, speed }) => {
                const elements = this.heroSection.querySelectorAll(selector);
                elements.forEach(element => {
                    const yPos = -(scrollY * speed);
                    element.style.transform = `translate3d(0, ${yPos}px, 0)`;
                });
            });
        };

        // Throttled scroll handler
        let ticking = false;
        const throttledScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', throttledScroll, { passive: true });
    }

    /**
     * Initialize mouse interaction effects
     */
    initializeMouseInteractions() {
        const energyField = this.heroSection.querySelector('.energy-field-ultimate');
        if (!energyField) return;

        let mouseX = 0;
        let mouseY = 0;
        let isMouseInside = false;

        const handleMouseMove = (e) => {
            const rect = this.heroSection.getBoundingClientRect();
            mouseX = (e.clientX - rect.left) / rect.width;
            mouseY = (e.clientY - rect.top) / rect.height;
            
            // Update energy field position based on mouse
            const offsetX = (mouseX - 0.5) * 20;
            const offsetY = (mouseY - 0.5) * 20;
            
            energyField.style.transform = `translate(-50%, -50%) translate(${offsetX}px, ${offsetY}px)`;
        };

        const handleMouseEnter = () => {
            isMouseInside = true;
            this.heroSection.classList.add('mouse-active');
        };

        const handleMouseLeave = () => {
            isMouseInside = false;
            this.heroSection.classList.remove('mouse-active');
            
            // Reset energy field position
            energyField.style.transform = 'translate(-50%, -50%)';
        };

        this.heroSection.addEventListener('mousemove', handleMouseMove);
        this.heroSection.addEventListener('mouseenter', handleMouseEnter);
        this.heroSection.addEventListener('mouseleave', handleMouseLeave);
    }

    /**
     * Initialize keyboard navigation for accessibility
     */
    initializeKeyboardNavigation() {
        const ctaButtons = this.heroSection.querySelectorAll('.cta-button-ultimate');
        
        ctaButtons.forEach(button => {
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.triggerButtonRipple(button);
                    setTimeout(() => {
                        button.click();
                    }, 150);
                }
            });

            button.addEventListener('focus', () => {
                button.classList.add('keyboard-focus');
            });

            button.addEventListener('blur', () => {
                button.classList.remove('keyboard-focus');
            });
        });
    }

    /**
     * Trigger button ripple effect
     */
    triggerButtonRipple(button) {
        const ripple = button.querySelector('.button-ripple-effect');
        if (!ripple) return;

        ripple.style.width = '0';
        ripple.style.height = '0';
        
        setTimeout(() => {
            ripple.style.width = '300px';
            ripple.style.height = '300px';
        }, 10);
    }

    /**
     * Animate scroll indicator
     */
    animateScrollIndicator(element) {
        const pulse = element.querySelector('.scroll-pulse');
        if (!pulse) return;

        // Add continuous bounce animation
        setInterval(() => {
            pulse.style.animation = 'none';
            setTimeout(() => {
                pulse.style.animation = 'scrollPulse 2s ease-in-out infinite';
            }, 10);
        }, 4000);
    }

    /**
     * Performance optimizations
     */
    initializePerformanceOptimizations() {
        // Reduce animations on low-end devices
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
            this.heroSection.classList.add('reduced-animations');
        }

        // Pause animations when tab is not visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.heroSection.classList.add('paused-animations');
            } else {
                this.heroSection.classList.remove('paused-animations');
            }
        });

        // Intersection observer for performance
        const performanceObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.heroSection.classList.remove('out-of-view');
                } else {
                    this.heroSection.classList.add('out-of-view');
                }
            });
        }, { threshold: 0 });

        performanceObserver.observe(this.heroSection);
        this.observers.push(performanceObserver);
    }

    /**
     * Cleanup method
     */
    destroy() {
        // Cancel animation frame
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }

        // Disconnect observers
        this.observers.forEach(observer => observer.disconnect());
        this.observers = [];

        // Remove event listeners
        window.removeEventListener('scroll', this.throttledScroll);
        
        this.isInitialized = false;
        console.log('Hero Ultimate Animations destroyed');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new HeroUltimateAnimations();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeroUltimateAnimations;
}
