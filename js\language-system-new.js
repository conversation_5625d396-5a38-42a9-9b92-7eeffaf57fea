/**
 * INOVA Energy - Professional Language Switching System
 * Fast and reliable language switching with RTL/LTR support
 */

class InovaLanguageSystem {
    constructor() {
        this.currentLang = this.getCurrentLanguage();
        this.isTransitioning = false;
        this.translations = this.getTranslations();
        this.init();
    }

    init() {
        this.createLanguageButton();
        this.applyLanguage(this.currentLang);
        this.bindEvents();
        console.log('INOVA Language System initialized:', this.currentLang);
    }

    getCurrentLanguage() {
        // Check localStorage first, then default to Persian
        return localStorage.getItem('inova_language') || 'fa';
    }

    createLanguageButton() {
        // Remove existing language buttons
        const existingButtons = document.querySelectorAll('.lang-toggle, .mobile-lang-toggle, .lang-switch');
        existingButtons.forEach(btn => btn.remove());

        // Create new language button
        const langButton = document.createElement('button');
        langButton.className = 'inova-lang-switch';
        langButton.innerHTML = `
            <span class="lang-icon">${this.currentLang === 'fa' ? '🇺🇸' : '🇮🇷'}</span>
            <span class="lang-text">${this.currentLang === 'fa' ? 'EN' : 'فا'}</span>
        `;
        langButton.setAttribute('aria-label', this.currentLang === 'fa' ? 'Switch to English' : 'تغییر به فارسی');

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .inova-lang-switch {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 10px 16px;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(0, 255, 209, 0.3);
                border-radius: 25px;
                color: var(--text-light);
                font-size: 0.9rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
                position: relative;
                z-index: 1001;
            }
            
            .inova-lang-switch:hover {
                background: rgba(0, 255, 209, 0.2);
                border-color: rgba(0, 255, 209, 0.5);
                transform: translateY(-2px);
            }
            
            .inova-lang-switch .lang-icon {
                font-size: 1.2em;
            }
            
            .inova-lang-switch.transitioning {
                opacity: 0.6;
                pointer-events: none;
            }
        `;
        document.head.appendChild(style);

        // Insert into header
        const headerContainer = document.querySelector('.header-container');
        if (headerContainer) {
            headerContainer.appendChild(langButton);
        }

        return langButton;
    }

    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('.inova-lang-switch')) {
                e.preventDefault();
                this.switchLanguage();
            }
        });
    }

    switchLanguage() {
        if (this.isTransitioning) return;

        this.isTransitioning = true;
        const newLang = this.currentLang === 'fa' ? 'en' : 'fa';
        
        // Add transition class
        document.body.classList.add('language-transitioning');
        
        // Update button state
        const langButton = document.querySelector('.inova-lang-switch');
        if (langButton) {
            langButton.classList.add('transitioning');
        }

        // Apply new language after short delay for smooth transition
        setTimeout(() => {
            this.currentLang = newLang;
            this.applyLanguage(newLang);
            
            // Save to localStorage
            localStorage.setItem('inova_language', newLang);
            
            // Update button
            this.updateLanguageButton();
            
            // Remove transition classes
            setTimeout(() => {
                document.body.classList.remove('language-transitioning');
                if (langButton) {
                    langButton.classList.remove('transitioning');
                }
                this.isTransitioning = false;
            }, 300);
            
        }, 150);
    }

    applyLanguage(lang) {
        // Update HTML attributes
        document.documentElement.lang = lang === 'fa' ? 'fa-IR' : 'en-US';
        document.documentElement.dir = lang === 'fa' ? 'rtl' : 'ltr';

        // Update body classes
        document.body.classList.remove('lang-fa', 'lang-en', 'rtl', 'ltr');
        document.body.classList.add(`lang-${lang}`, lang === 'fa' ? 'rtl' : 'ltr');

        // Update page title
        document.title = lang === 'fa' ? 
            'INOVA Energy | پیشگام در صنعت انرژی پایدار' : 
            'INOVA Energy | Leading Sustainable Energy Innovation';

        // Update meta description
        const metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc) {
            metaDesc.content = lang === 'fa' ? 
                'گروه بین‌المللی INOVA Energy پیشگام در صنعت انرژی پایدار، پتروشیمی و فناوری‌های نوین' :
                'INOVA Energy international group, leading in sustainable energy, petrochemicals and innovative technologies';
        }

        // Update font family
        document.body.style.fontFamily = lang === 'fa' ? 
            "'Vazirmatn', 'Tahoma', sans-serif" : 
            "'Inter', 'Arial', sans-serif";

        // Translate content
        this.translateContent(lang);
    }

    translateContent(lang) {
        const translations = this.translations[lang];
        
        // Update all elements with data-translate attributes
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            if (translations[key]) {
                element.textContent = translations[key];
            }
        });

        // Update navigation menu
        this.updateNavigation(lang);
        
        // Update form placeholders
        this.updateFormPlaceholders(lang);
    }

    updateNavigation(lang) {
        const navItems = {
            fa: ['خانه', 'درباره ما', 'ریبرندینگ', 'فعالیت‌ها', 'خدمات', 'تماس'],
            en: ['Home', 'About', 'Rebranding', 'Activities', 'Services', 'Contact']
        };

        const navLinks = document.querySelectorAll('.nav-menu a, .mobile-nav-menu a');
        navLinks.forEach((link, index) => {
            if (navItems[lang][index]) {
                link.textContent = navItems[lang][index];
            }
        });
    }

    updateFormPlaceholders(lang) {
        const placeholders = {
            fa: {
                'firstName': 'نام',
                'lastName': 'نام خانوادگی',
                'email': 'ایمیل',
                'phone': 'تلفن',
                'company': 'شرکت',
                'message': 'پیام خود را اینجا بنویسید...'
            },
            en: {
                'firstName': 'First Name',
                'lastName': 'Last Name',
                'email': 'Email',
                'phone': 'Phone',
                'company': 'Company',
                'message': 'Write your message here...'
            }
        };

        Object.keys(placeholders[lang]).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.placeholder = placeholders[lang][id];
            }
        });
    }

    updateLanguageButton() {
        const langButton = document.querySelector('.inova-lang-switch');
        if (langButton) {
            langButton.innerHTML = `
                <span class="lang-icon">${this.currentLang === 'fa' ? '🇺🇸' : '🇮🇷'}</span>
                <span class="lang-text">${this.currentLang === 'fa' ? 'EN' : 'فا'}</span>
            `;
            langButton.setAttribute('aria-label', this.currentLang === 'fa' ? 'Switch to English' : 'تغییر به فارسی');
        }
    }

    getTranslations() {
        return {
            fa: {
                // Navigation
                'home': 'خانه',
                'about': 'درباره ما',
                'rebranding': 'ریبرندینگ',
                'activities': 'فعالیت‌ها',
                'services': 'خدمات',
                'contact': 'تماس',

                // Hero Section
                'hero_title_1': 'INOVA',
                'hero_title_2': 'Energy',
                'hero_title_3': 'پیشگام انرژی پایدار',
                'hero_description': 'گروه بین‌المللی سرمایه‌گذاری و توسعه در حوزه انرژی با بیش از 25 سال تجربه در صنایع نفت، گاز، پتروشیمی و انرژی‌های تجدیدپذیر',
                'explore_activities': 'کشف فعالیت‌ها',
                'contact_us': 'تماس با ما',
                'years_experience': 'سال تجربه',
                'main_activities': 'محور فعالیت',
                'established': 'تأسیس',
                'scroll_down': 'اسکرول کنید',

                // Common
                'company-name': 'اینووا انرژی',
                'learn_more': 'اطلاعات بیشتر'
            },
            en: {
                // Navigation
                'home': 'Home',
                'about': 'About',
                'rebranding': 'Rebranding',
                'activities': 'Activities',
                'services': 'Services',
                'contact': 'Contact',

                // Hero Section
                'hero_title_1': 'INOVA',
                'hero_title_2': 'Energy',
                'hero_title_3': 'Sustainable Energy Pioneer',
                'hero_description': 'International investment and development group in energy sector with over 25 years of experience in oil, gas, petrochemical and renewable energy industries',
                'explore_activities': 'Explore Activities',
                'contact_us': 'Contact Us',
                'years_experience': 'Years Experience',
                'main_activities': 'Main Activities',
                'established': 'Established',
                'scroll_down': 'Scroll Down',

                // Common
                'company-name': 'INOVA Energy',
                'learn_more': 'Learn More'
            }
        };
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.inovaLanguageSystem = new InovaLanguageSystem();
});

// Add transition styles
const transitionStyles = document.createElement('style');
transitionStyles.textContent = `
    body.language-transitioning * {
        transition: all 0.3s ease !important;
    }
`;
document.head.appendChild(transitionStyles);
