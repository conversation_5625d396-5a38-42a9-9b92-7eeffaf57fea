/* ===================================
   PROFESSIONAL SECTIONS STYLING
   Clean, Organized & Responsive Design
   =================================== */

/* Common Section Styles */
.about-section-professional,
.services-section-professional,
.activities-section-professional,
.projects-section-professional,
.rebranding-section-professional,
.contact-section-professional {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 1) 0%,
            rgba(0, 31, 63, 0.98) 50%,
            rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
    min-height: 100vh;
}

/* Background Systems */
.about-background,
.services-background,
.activities-background,
.projects-background,
.rebranding-background,
.contact-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.bg-gradient-primary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at 30% 20%, rgba(0, 163, 255, 0.15) 0%, transparent 70%);
}

.bg-gradient-secondary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at 70% 80%, rgba(0, 255, 198, 0.1) 0%, transparent 70%);
}

.bg-decorative {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.decorative-element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(0, 163, 255, 0.1), rgba(0, 255, 198, 0.1));
    animation: float 6s ease-in-out infinite;
}

.decorative-element.element-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.decorative-element.element-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 15%;
    animation-delay: 2s;
}

.decorative-element.element-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 20%;
    animation-delay: 4s;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* Container */
.container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Headers */
.about-header,
.services-header,
.activities-header,
.projects-header,
.rebranding-header,
.contact-header {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.3);
    border-radius: 50px;
    color: #00a3ff;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 1.1rem;
}

.section-title {
    margin-bottom: 24px;
}

.title-main {
    display: block;
    font-size: 3.5rem;
    font-weight: 800;
    color: #ffffff;
    line-height: 1.2;
    margin-bottom: 8px;
}

.title-sub {
    display: block;
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.3;
}

.gradient-text {
    background: linear-gradient(135deg, #00a3ff 0%, #00ffc6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto;
}

/* Grid Layouts */
.about-content-grid,
.services-grid,
.activities-grid,
.projects-grid {
    display: grid;
    gap: 40px;
    margin-bottom: 80px;
}

.about-content-grid {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
}

.services-grid,
.activities-grid,
.projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
}

/* Cards */
.story-card,
.values-card,
.service-card,
.activity-card,
.project-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 40px 30px;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.story-card:hover,
.values-card:hover,
.service-card:hover,
.activity-card:hover,
.project-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 163, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.1);
}

/* Card Headers */
.story-header,
.values-header,
.service-header,
.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.story-icon,
.values-icon,
.service-icon,
.activity-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(0, 163, 255, 0.2), rgba(0, 255, 198, 0.2));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00a3ff;
}

.story-icon svg,
.values-icon svg,
.service-icon svg,
.activity-icon svg {
    width: 28px;
    height: 28px;
}

.service-number,
.activity-number {
    font-size: 2rem;
    font-weight: 800;
    color: rgba(0, 163, 255, 0.3);
}

/* Card Titles */
.story-title,
.values-title,
.service-title,
.activity-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 16px;
}

/* Card Content */
.service-description,
.activity-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 24px;
}

/* Features */
.service-features,
.activity-features {
    margin-bottom: 24px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.feature-icon {
    color: #00ffc6;
    font-weight: 600;
}

.feature-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
}

/* Card Footers */
.service-footer {
    margin-top: auto;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #00a3ff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.service-link:hover {
    color: #00ffc6;
    transform: translateX(5px);
}

.link-arrow {
    transition: transform 0.3s ease;
}

.service-link:hover .link-arrow {
    transform: translateX(5px);
}

/* CTA Sections */
.services-cta,
.activities-cta {
    text-align: center;
    padding: 60px 40px;
    background: rgba(0, 163, 255, 0.05);
    border: 1px solid rgba(0, 163, 255, 0.1);
    border-radius: 24px;
    backdrop-filter: blur(20px);
}

.cta-title {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 16px;
}

.cta-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 32px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 16px 32px;
    background: linear-gradient(135deg, #00a3ff, #00ffc6);
    color: #000;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 163, 255, 0.3);
}

.button-icon svg {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.cta-button:hover .button-icon svg {
    transform: rotate(45deg);
}

/* Statistics */
.about-statistics {
    margin-bottom: 80px;
}

.stats-header {
    text-align: center;
    margin-bottom: 60px;
}

.stats-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 16px;
}

.stats-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.stat-item {
    text-align: center;
    padding: 40px 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    border-color: rgba(0, 163, 255, 0.3);
}

.stat-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, rgba(0, 163, 255, 0.2), rgba(0, 255, 198, 0.2));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00a3ff;
}

.stat-icon svg {
    width: 28px;
    height: 28px;
}

.stat-content {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 4px;
    margin-bottom: 12px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: #00a3ff;
}

.stat-plus {
    font-size: 1.5rem;
    font-weight: 600;
    color: #00ffc6;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Vision Section */
.about-vision {
    margin-bottom: 40px;
}

.vision-card {
    background: rgba(0, 163, 255, 0.05);
    border: 1px solid rgba(0, 163, 255, 0.1);
    border-radius: 24px;
    padding: 60px 40px;
    text-align: center;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.vision-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.vision-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(0, 163, 255, 0.1) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.5;
    }

    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.8;
    }
}

.vision-content {
    position: relative;
    z-index: 2;
}

.vision-header {
    margin-bottom: 32px;
}

.vision-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    background: linear-gradient(135deg, rgba(0, 163, 255, 0.2), rgba(0, 255, 198, 0.2));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00a3ff;
}

.vision-icon svg {
    width: 40px;
    height: 40px;
}

.vision-title {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
}

.vision-text {
    margin-bottom: 40px;
}

.vision-text p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto;
}

.vision-goals {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
}

.goal-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.goal-item:hover {
    background: rgba(0, 163, 255, 0.1);
    transform: translateY(-2px);
}

.goal-icon {
    font-size: 1.5rem;
}

.goal-text {
    font-size: 0.95rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
    .container {
        max-width: 1600px;
        padding: 0 60px;
    }

    .title-main {
        font-size: 4rem;
    }

    .title-sub {
        font-size: 2.5rem;
    }
}

/* Desktop (1200px - 1399px) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .container {
        padding: 0 50px;
    }

    .about-content-grid {
        grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    }

    .services-grid,
    .activities-grid,
    .projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* Tablet Landscape (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {

    .about-section-professional,
    .services-section-professional,
    .activities-section-professional,
    .projects-section-professional,
    .rebranding-section-professional,
    .contact-section-professional {
        padding: 80px 0;
    }

    .container {
        padding: 0 40px;
    }

    .title-main {
        font-size: 3rem;
    }

    .title-sub {
        font-size: 1.8rem;
    }

    .about-content-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .services-grid,
    .activities-grid,
    .projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet Portrait (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {

    .about-section-professional,
    .services-section-professional,
    .activities-section-professional,
    .projects-section-professional,
    .rebranding-section-professional,
    .contact-section-professional {
        padding: 60px 0;
    }

    .container {
        padding: 0 30px;
    }

    .about-header,
    .services-header,
    .activities-header,
    .projects-header,
    .rebranding-header,
    .contact-header {
        margin-bottom: 60px;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.5rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .services-grid,
    .activities-grid,
    .projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .story-card,
    .values-card,
    .service-card,
    .activity-card,
    .project-card {
        padding: 30px 25px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .vision-goals {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile Large (576px - 767px) */
@media (max-width: 767px) and (min-width: 576px) {

    .about-section-professional,
    .services-section-professional,
    .activities-section-professional,
    .projects-section-professional,
    .rebranding-section-professional,
    .contact-section-professional {
        padding: 50px 0;
    }

    .container {
        padding: 0 20px;
    }

    .about-header,
    .services-header,
    .activities-header,
    .projects-header,
    .rebranding-header,
    .contact-header {
        margin-bottom: 50px;
    }

    .title-main {
        font-size: 2.2rem;
    }

    .title-sub {
        font-size: 1.3rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .about-content-grid,
    .services-grid,
    .activities-grid,
    .projects-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 60px;
    }

    .story-card,
    .values-card,
    .service-card,
    .activity-card,
    .project-card {
        padding: 25px 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .vision-goals {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .services-cta,
    .activities-cta {
        padding: 40px 20px;
    }

    .cta-title {
        font-size: 1.8rem;
    }
}

/* Mobile Small (320px - 575px) */
@media (max-width: 575px) {

    .about-section-professional,
    .services-section-professional,
    .activities-section-professional,
    .projects-section-professional,
    .rebranding-section-professional,
    .contact-section-professional {
        padding: 40px 0;
    }

    .container {
        padding: 0 15px;
    }

    .about-header,
    .services-header,
    .activities-header,
    .projects-header,
    .rebranding-header,
    .contact-header {
        margin-bottom: 40px;
    }

    .header-badge {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .title-main {
        font-size: 1.9rem;
        line-height: 1.2;
    }

    .title-sub {
        font-size: 1.1rem;
    }

    .section-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .about-content-grid,
    .services-grid,
    .activities-grid,
    .projects-grid {
        gap: 15px;
        margin-bottom: 40px;
    }

    .story-card,
    .values-card,
    .service-card,
    .activity-card,
    .project-card {
        padding: 20px 15px;
    }

    .story-icon,
    .values-icon,
    .service-icon,
    .activity-icon {
        width: 50px;
        height: 50px;
    }

    .story-icon svg,
    .values-icon svg,
    .service-icon svg,
    .activity-icon svg {
        width: 24px;
        height: 24px;
    }

    .story-title,
    .values-title,
    .service-title,
    .activity-title {
        font-size: 1.3rem;
    }

    .service-description,
    .activity-description {
        font-size: 0.9rem;
    }

    .stats-grid {
        gap: 15px;
    }

    .stat-item {
        padding: 30px 15px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .vision-card {
        padding: 40px 20px;
    }

    .vision-icon {
        width: 60px;
        height: 60px;
    }

    .vision-icon svg {
        width: 30px;
        height: 30px;
    }

    .vision-title {
        font-size: 1.6rem;
    }

    .vision-text p {
        font-size: 1rem;
    }

    .services-cta,
    .activities-cta {
        padding: 30px 15px;
    }

    .cta-title {
        font-size: 1.5rem;
    }

    .cta-description {
        font-size: 1rem;
    }

    .cta-button {
        padding: 14px 24px;
        font-size: 1rem;
    }
}