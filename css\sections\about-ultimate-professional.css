/* ===================================
   ABOUT SECTION - ULTIMATE PROFESSIONAL DESIGN
   Matching Hero Section Style
   =================================== */

/* ===================================
   ABOUT SECTION BASE
   =================================== */

.about-section-ultimate {
    position: relative;
    width: 100vw;
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0a0a0a;
    z-index: 1;
}

/* ===================================
   ABOUT BACKGROUND SYSTEM
   =================================== */

.about-background-ultimate {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

/* Base Gradient Layers */
.about-bg-gradient-base {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.about-gradient-primary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse 700px 500px at 80% 20%,
            rgba(0, 163, 255, 0.12) 0%,
            transparent 60%),
        radial-gradient(ellipse 500px 700px at 20% 80%,
            rgba(0, 255, 209, 0.1) 0%,
            transparent 60%),
        radial-gradient(ellipse 900px 300px at 50% 50%,
            rgba(138, 43, 226, 0.06) 0%,
            transparent 70%),
        linear-gradient(45deg,
            rgba(0, 0, 0, 0.9) 0%,
            rgba(10, 10, 10, 0.95) 50%,
            rgba(0, 0, 0, 1) 100%);
    animation: aboutGradientPulse 20s ease-in-out infinite;
}

.about-gradient-secondary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(from 180deg at 50% 50%,
            rgba(0, 255, 209, 0.06) 0deg,
            rgba(0, 163, 255, 0.03) 120deg,
            rgba(138, 43, 226, 0.04) 240deg,
            rgba(0, 255, 209, 0.06) 360deg);
    animation: aboutGradientRotate 30s linear infinite;
}

/* ===================================
   ABOUT DECORATIVE ELEMENTS
   =================================== */

.about-decorative-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    opacity: 0.2;
}

.about-logo-watermark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 250px;
    height: 250px;
    background-image: url('../../../Logo1.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.04;
    animation: aboutLogoWatermark 25s ease-in-out infinite;
}

.about-floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.about-element {
    position: absolute;
    width: 30px;
    height: 30px;
    background: rgba(0, 255, 209, 0.08);
    border: 1px solid rgba(0, 255, 209, 0.2);
    border-radius: 8px;
    animation: aboutElementFloat 18s ease-in-out infinite;
}

.about-element.element-1 {
    top: 20%;
    right: 15%;
    animation-delay: 0s;
}

.about-element.element-2 {
    top: 70%;
    left: 10%;
    animation-delay: 6s;
}

.about-element.element-3 {
    bottom: 25%;
    right: 25%;
    animation-delay: 12s;
}

/* ===================================
   ABOUT CONTENT CONTAINER
   =================================== */

.about-content-ultimate {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 clamp(20px, 5vw, 80px);
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    justify-content: center;
    gap: clamp(40px, 8vw, 100px);
    height: 100vh;
}

/* ===================================
   ABOUT LEFT SIDE - CONTENT
   =================================== */

.about-main-content-ultimate {
    grid-column: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: clamp(25px, 5vh, 40px);
    text-align: left;
    height: 100%;
}

.about-header-ultimate {
    width: 100%;
}

.about-title-ultimate {
    margin: 0 0 clamp(15px, 3vh, 25px) 0;
    line-height: 1.1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: clamp(5px, 1vh, 10px);
}

.about-title-word {
    display: block;
}

.about-title-word.word-1 {
    font-size: clamp(1.8rem, 6vw, 3rem);
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.about-title-word.word-2 {
    font-size: clamp(2.2rem, 7vw, 3.5rem);
    font-weight: 800;
    background: linear-gradient(135deg, #00a3ff 0%, #00ffd1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-description-ultimate {
    font-size: clamp(1rem, 2.2vw, 1.2rem);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* ===================================
   ABOUT RIGHT SIDE - FEATURES
   =================================== */

.about-features-ultimate {
    grid-column: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: clamp(20px, 4vh, 30px);
    height: 100%;
}

.about-features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: clamp(15px, 3vw, 25px);
    width: 100%;
    max-width: 400px;
}

.about-feature-card {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: clamp(15px, 3vh, 20px);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    aspect-ratio: 1;
    justify-content: center;
    gap: clamp(8px, 1.5vh, 12px);
    text-align: center;
}

.about-feature-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 255, 209, 0.3);
    box-shadow: 0 8px 25px rgba(0, 255, 209, 0.15);
}

.about-feature-icon {
    width: clamp(24px, 4vw, 32px);
    height: clamp(24px, 4vw, 32px);
    color: #00ffd1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 209, 0.1);
    border-radius: 8px;
    padding: 6px;
    transition: all 0.3s ease;
}

.about-feature-icon svg {
    width: 100%;
    height: 100%;
}

.about-feature-card:hover .about-feature-icon {
    background: rgba(0, 255, 209, 0.2);
    color: #ffffff;
    transform: scale(1.1);
}

.about-feature-title {
    font-size: clamp(0.9rem, 2vw, 1.1rem);
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    line-height: 1.2;
}

.about-feature-desc {
    font-size: clamp(0.8rem, 1.8vw, 0.95rem);
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    line-height: 1.3;
}

/* ===================================
   ABOUT ANIMATIONS
   =================================== */

@keyframes aboutGradientPulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.02) rotate(2deg);
    }
}

@keyframes aboutGradientRotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes aboutLogoWatermark {

    0%,
    100% {
        opacity: 0.04;
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
    }

    50% {
        opacity: 0.06;
        transform: translate(-50%, -50%) scale(1.05) rotate(3deg);
    }
}

@keyframes aboutElementFloat {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.5;
    }

    25% {
        transform: translateY(-10px) rotate(3deg);
        opacity: 0.7;
    }

    50% {
        transform: translateY(-5px) rotate(-2deg);
        opacity: 0.8;
    }

    75% {
        transform: translateY(-15px) rotate(5deg);
        opacity: 0.6;
    }
}

/* ===================================
   ABOUT RESPONSIVE DESIGN
   =================================== */

/* Tablet and Mobile - Stack Layout */
@media (max-width: 991px) {
    .about-content-ultimate {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto;
        gap: clamp(30px, 6vh, 50px);
        text-align: center;
    }

    .about-main-content-ultimate {
        grid-column: 1;
        grid-row: 1;
        align-items: center;
        text-align: center;
    }

    .about-title-ultimate {
        align-items: center;
    }

    .about-features-ultimate {
        grid-column: 1;
        grid-row: 2;
    }

    .about-features-grid {
        max-width: 350px;
    }
}

/* Mobile Small */
@media (max-width: 575px) {
    .about-content-ultimate {
        gap: clamp(20px, 4vh, 30px);
        padding: 0 clamp(15px, 4vw, 30px);
    }

    .about-main-content-ultimate {
        gap: clamp(15px, 3vh, 25px);
    }

    .about-features-grid {
        grid-template-columns: 1fr;
        max-width: 250px;
        gap: clamp(10px, 2vw, 15px);
    }

    .about-feature-card {
        aspect-ratio: auto;
        padding: clamp(12px, 2.5vh, 16px);
    }

    .about-logo-watermark {
        width: 150px;
        height: 150px;
        opacity: 0.02;
    }

    .about-element {
        width: 20px;
        height: 20px;
    }
}