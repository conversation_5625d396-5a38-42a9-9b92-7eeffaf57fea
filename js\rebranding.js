// Animate numbers when in viewport
const animateNumbers = () => {
    const stats = document.querySelectorAll('.stat-number');

    stats.forEach(stat => {
        // Get target value from data-count attribute or parse from text content
        let target = parseInt(stat.getAttribute('data-count'), 10);

        // If no data-count attribute, try to parse from text content
        if (isNaN(target)) {
            const textContent = stat.textContent.trim();
            const numberMatch = textContent.match(/\d+/);
            if (numberMatch) {
                target = parseInt(numberMatch[0], 10);
            } else {
                console.warn('No valid number found for stat element:', stat);
                return; // Skip this element
            }
        }

        // Only animate if we have a valid target
        if (isNaN(target) || target <= 0) return;

        // Store original suffix (like "+")
        const originalText = stat.textContent.trim();
        const suffix = originalText.replace(/\d+/, '').trim();

        const duration = 2000; // 2 seconds
        const step = target / (duration / 16); // 60fps
        let current = 0;

        const updateNumber = () => {
            current += step;
            if (current < target) {
                stat.textContent = Math.floor(current) + suffix;
                requestAnimationFrame(updateNumber);
            } else {
                stat.textContent = target + suffix;
            }
        };

        updateNumber();
    });
};

// Initialize particles.js only if available
if (typeof particlesJS !== 'undefined' && document.getElementById('particles-js')) {
    particlesJS('particles-js', {
        particles: {
            number: {
                value: 80,
                density: {
                    enable: true,
                    value_area: 800
                }
            },
            color: {
                value: '#00a3ff'
            },
            shape: {
                type: 'circle'
            },
            opacity: {
                value: 0.5,
                random: false
            },
            size: {
                value: 3,
                random: true
            },
            line_linked: {
                enable: true,
                distance: 150,
                color: '#00a3ff',
                opacity: 0.2,
                width: 1
            },
            move: {
                enable: true,
                speed: 2,
                direction: 'none',
                random: false,
                straight: false,
                out_mode: 'out',
                bounce: false
            }
        },
        interactivity: {
            detect_on: 'canvas',
            events: {
                onhover: {
                    enable: true,
                    mode: 'grab'
                },
                resize: true
            },
            modes: {
                grab: {
                    distance: 140,
                    line_linked: {
                        opacity: 1
                    }
                }
            }
        },
        retina_detect: true
    });

    // Intersection Observer for timeline items
    const observeTimeline = () => {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, { threshold: 0.5 });

        document.querySelectorAll('.timeline-item').forEach(item => {
            observer.observe(item);
        });
    };

    // Initialize everything when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        observeTimeline();

        // Animate numbers when section comes into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                    observer.unobserve(entry.target);
                }
            });
        });

        const statsContainer = document.querySelector('.stats-container');
        if (statsContainer) {
            observer.observe(statsContainer);
        }
    });
}