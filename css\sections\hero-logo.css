/* Hero Logo Column - Contained */
.hero-logo-column {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    max-height: min(600px, 70vh);
    overflow: hidden;
}

.logo-animation-container {
    width: min(500px, 45vw);
    height: min(500px, 45vw);
    position: relative;
    perspective: 1000px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    animation: floatLogo 6s ease-in-out infinite;
}

/* Logo Container */
.logo-container {
    position: absolute;
    width: 60%;
    height: 60%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.main-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 0 20px rgba(var(--primary-rgb), 0.3));
}

/* Advanced Effects Container */
.advanced-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Orbital Paths */
.orbital-paths {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.orbit {
    fill: none;
    stroke: rgba(var(--primary-rgb), 0.2);
    stroke-width: 1;
    stroke-dasharray: 10 5;
    animation: orbitRotate 20s linear infinite;
}

.orbit-1 {
    animation-duration: 30s;
}

.orbit-2 {
    animation-duration: 25s;
}

.orbit-3 {
    animation-duration: 20s;
}

/* Floating Particles */
.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 30%, rgba(var(--primary-rgb), 0.3) 0%, transparent 5%),
        radial-gradient(circle at 70% 70%, rgba(var(--accent-rgb), 0.3) 0%, transparent 5%);
    filter: blur(1px);
    animation: floatParticles 8s ease-in-out infinite;
}

/* Animations */
@keyframes floatLogo {

    0%,
    100% {
        transform: translateY(0) rotateX(0deg);
    }

    50% {
        transform: translateY(-20px) rotateX(5deg);
    }
}

@keyframes orbitRotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

@keyframes floatParticles {

    0%,
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.6;
    }

    50% {
        transform: scale(1.1) rotate(180deg);
        opacity: 0.8;
    }
}

/* Responsive Design - Optimized Containment */
@media (max-width: 1200px) {
    .hero-section {
        padding: clamp(1.5rem, 3vh, 4rem) clamp(1rem, 2vw, 1.5rem);
    }

    .logo-animation-container {
        width: min(400px, 40vw);
        height: min(400px, 40vw);
    }

    .hero-grid {
        gap: clamp(1rem, 2vw, 3rem);
    }
}

@media (max-width: 992px) {
    .hero-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .hero-content {
        height: auto;
        min-height: min(800px, 90vh);
    }

    .hero-logo-column {
        margin-top: 0;
        height: min(400px, 50vh);
    }

    .logo-animation-container {
        width: min(350px, 60vw);
        height: min(350px, 60vw);
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: clamp(1rem, 2vh, 3rem) clamp(0.8rem, 1.5vw, 1rem);
    }

    .logo-animation-container {
        width: min(300px, 70vw);
        height: min(300px, 70vw);
    }

    .hero-description-wrapper {
        padding: clamp(0.8rem, 1.5vw, 1.5rem);
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: 1rem 0.5rem;
    }

    .logo-animation-container {
        width: min(250px, 80vw);
        height: min(250px, 80vw);
    }

    .hero-content {
        max-width: 100%;
    }
}

/* CSS Variables */
:root {
    --primary: #00a3ff;
    --primary-rgb: 0, 163, 255;
    --accent: #00ffd1;
    --accent-rgb: 0, 255, 209;
    --glow-intensity: 0.5;
    --animation-speed: 1;
}