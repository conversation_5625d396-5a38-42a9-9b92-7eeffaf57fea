/* ===================================
   INOVA ENERGY HERO SECTION - ULTIMATE PROFESSIONAL DESIGN
   Advanced animated background system with energy effects
   =================================== */

/* ===================================
   HERO SECTION BASE
   =================================== */

.hero-ultimate-professional {
    position: relative;
    width: 100vw;
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0a0a0a;
    z-index: 1;
}

/* ===================================
   ADVANCED BACKGROUND SYSTEM
   =================================== */

.hero-background-ultimate {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

/* Base Gradient Layers */
.bg-gradient-base {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.gradient-primary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse 800px 600px at 25% 15%,
            rgba(0, 163, 255, 0.15) 0%,
            transparent 60%),
        radial-gradient(ellipse 600px 800px at 75% 85%,
            rgba(0, 255, 209, 0.12) 0%,
            transparent 60%),
        radial-gradient(ellipse 1000px 400px at 50% 50%,
            rgba(138, 43, 226, 0.08) 0%,
            transparent 70%),
        linear-gradient(135deg,
            rgba(0, 0, 0, 0.9) 0%,
            rgba(10, 10, 10, 0.95) 50%,
            rgba(0, 0, 0, 1) 100%);
    animation: gradientPulse 15s ease-in-out infinite;
}

.gradient-secondary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(from 0deg at 50% 50%,
            rgba(0, 255, 209, 0.1) 0deg,
            rgba(0, 163, 255, 0.05) 90deg,
            rgba(138, 43, 226, 0.08) 180deg,
            rgba(0, 255, 209, 0.1) 270deg,
            rgba(0, 255, 209, 0.1) 360deg);
    animation: gradientRotate 20s linear infinite;
}

.gradient-accent {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
            rgba(0, 255, 209, 0.03) 0%,
            transparent 25%,
            transparent 75%,
            rgba(0, 163, 255, 0.03) 100%);
    animation: gradientShift 12s ease-in-out infinite;
}

/* ===================================
   ENERGY FIELD SYSTEM
   =================================== */

.energy-field-ultimate {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    height: 800px;
    z-index: 2;
}

.energy-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background-image: url('../../../Logo1.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 50%;
    box-shadow:
        0 0 30px rgba(0, 255, 209, 0.4),
        0 0 60px rgba(0, 255, 209, 0.2),
        0 0 120px rgba(0, 255, 209, 0.1);
    animation: logoGlow 4s ease-in-out infinite;
    opacity: 0.3;
}

/* Energy Transformation Particles */
.energy-transformation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    pointer-events: none;
}

.energy-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    opacity: 0;
}

.energy-particle.dirty {
    background: rgba(128, 128, 128, 0.6);
    animation: dirtyToClean 3s ease-in-out infinite;
}

.energy-particle.clean {
    background: rgba(0, 255, 209, 0.8);
    box-shadow: 0 0 10px rgba(0, 255, 209, 0.6);
    animation: cleanEnergyFlow 3s ease-in-out infinite;
}

.energy-rings-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.energy-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    border: 1px solid rgba(0, 255, 209, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.energy-ring.ring-primary {
    width: 200px;
    height: 200px;
    border-color: rgba(0, 255, 209, 0.4);
    animation: ringRotate 15s linear infinite;
}

.energy-ring.ring-secondary {
    width: 350px;
    height: 350px;
    border-color: rgba(0, 163, 255, 0.3);
    animation: ringRotate 20s linear infinite reverse;
}

.energy-ring.ring-tertiary {
    width: 500px;
    height: 500px;
    border-color: rgba(138, 43, 226, 0.25);
    animation: ringRotate 25s linear infinite;
}

.energy-ring.ring-quaternary {
    width: 650px;
    height: 650px;
    border-color: rgba(0, 255, 209, 0.2);
    animation: ringRotate 30s linear infinite reverse;
}

.energy-pulses {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(0, 255, 209, 0.6);
    border-radius: 50%;
    animation: pulseExpand 4s ease-out infinite;
}

.pulse.pulse-1 {
    animation-delay: 0s;
}

.pulse.pulse-2 {
    animation-delay: 1.3s;
}

.pulse.pulse-3 {
    animation-delay: 2.6s;
}

/* ===================================
   SIMPLIFIED PARTICLE SYSTEM
   =================================== */

.particle-system-ultimate {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    opacity: 0.3;
}

.particle-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.particles-floating {
    background-image:
        radial-gradient(2px 2px at 25px 35px, rgba(0, 255, 209, 0.4), transparent),
        radial-gradient(1px 1px at 65px 15px, rgba(0, 163, 255, 0.3), transparent),
        radial-gradient(2px 2px at 120px 60px, rgba(138, 43, 226, 0.2), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particleFloat 30s linear infinite;
}

.particles-energy {
    display: none;
    /* Disabled for simplicity */
}

.particles-glow {
    display: none;
    /* Disabled for simplicity */
}

/* ===================================
   SIMPLIFIED GEOMETRIC ELEMENTS
   =================================== */

.geometric-art-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    opacity: 0.2;
}

.geo-grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 209, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 209, 0.05) 1px, transparent 1px);
    background-size: 80px 80px;
    animation: gridShift 60s linear infinite;
}

.geo-hexagon-cluster,
.geo-line-network,
.geo-dot-matrix {
    display: none;
    /* Simplified - removed extra elements */
}

/* ===================================
   SIMPLIFIED WAVE SYSTEM
   =================================== */

.wave-system-ultimate {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    z-index: 2;
    opacity: 0.3;
}

.wave-layer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100%;
}

.wave-primary {
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120"><path d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z" fill="rgba(0,255,209,0.05)"/></svg>');
    background-size: 1200px 120px;
    animation: waveMove 40s linear infinite;
}

.wave-secondary,
.wave-accent {
    display: none;
    /* Simplified - only one wave */
}

/* ===================================
   BACKGROUND ANIMATIONS
   =================================== */

@keyframes gradientPulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes gradientRotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes gradientShift {

    0%,
    100% {
        transform: translateX(0) translateY(0);
    }

    25% {
        transform: translateX(10px) translateY(-5px);
    }

    50% {
        transform: translateX(-5px) translateY(10px);
    }

    75% {
        transform: translateX(-10px) translateY(-10px);
    }
}

@keyframes coreGlow {

    0%,
    100% {
        box-shadow:
            0 0 20px rgba(0, 255, 209, 0.6),
            0 0 40px rgba(0, 255, 209, 0.4),
            0 0 80px rgba(0, 255, 209, 0.2);
    }

    50% {
        box-shadow:
            0 0 30px rgba(0, 255, 209, 0.8),
            0 0 60px rgba(0, 255, 209, 0.6),
            0 0 120px rgba(0, 255, 209, 0.4);
    }
}

@keyframes ringRotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes pulseExpand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }

    100% {
        width: 300px;
        height: 300px;
        opacity: 0;
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(0) translateX(0);
    }

    25% {
        transform: translateY(-20px) translateX(10px);
    }

    50% {
        transform: translateY(-10px) translateX(-15px);
    }

    75% {
        transform: translateY(-30px) translateX(5px);
    }

    100% {
        transform: translateY(0) translateX(0);
    }
}

@keyframes particleEnergy {
    0% {
        transform: translateY(0) rotate(0deg);
    }

    100% {
        transform: translateY(-100vh) rotate(360deg);
    }
}

@keyframes particleGlow {

    0%,
    100% {
        opacity: 0.2;
        transform: scale(1);
    }

    50% {
        opacity: 0.6;
        transform: scale(1.2);
    }
}

@keyframes gridShift {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(50px, 50px);
    }
}

@keyframes hexagonFloat {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
    }

    25% {
        transform: translateY(-20px) rotate(90deg);
    }

    50% {
        transform: translateY(-10px) rotate(180deg);
    }

    75% {
        transform: translateY(-30px) rotate(270deg);
    }
}

@keyframes networkPulse {

    0%,
    100% {
        opacity: 0.2;
        transform: scale(1);
    }

    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

@keyframes dotMatrix {

    0%,
    100% {
        transform: rotate(0deg) scale(1);
    }

    50% {
        transform: rotate(180deg) scale(1.1);
    }
}

@keyframes waveMove {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-1200px);
    }
}

@keyframes logoGlow {

    0%,
    100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
        box-shadow:
            0 0 30px rgba(0, 255, 209, 0.4),
            0 0 60px rgba(0, 255, 209, 0.2),
            0 0 120px rgba(0, 255, 209, 0.1);
    }

    50% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(1.05);
        box-shadow:
            0 0 40px rgba(0, 255, 209, 0.6),
            0 0 80px rgba(0, 255, 209, 0.4),
            0 0 160px rgba(0, 255, 209, 0.2);
    }
}

@keyframes dirtyToClean {
    0% {
        opacity: 0.8;
        background: rgba(128, 128, 128, 0.6);
        transform: translateY(0) scale(1);
    }

    50% {
        opacity: 1;
        background: rgba(64, 64, 64, 0.8);
        transform: translateY(-20px) scale(1.2);
    }

    100% {
        opacity: 0;
        background: rgba(0, 255, 209, 0.8);
        transform: translateY(-40px) scale(0.5);
        box-shadow: 0 0 15px rgba(0, 255, 209, 0.8);
    }
}

@keyframes cleanEnergyFlow {
    0% {
        opacity: 0;
        transform: translateY(-40px) scale(0.5);
    }

    50% {
        opacity: 1;
        transform: translateY(-60px) scale(1);
    }

    100% {
        opacity: 0;
        transform: translateY(-80px) scale(0.3);
    }
}