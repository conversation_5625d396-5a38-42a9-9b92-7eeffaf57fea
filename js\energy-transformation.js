/**
 * INOVA Energy - Energy Transformation Animation
 * Beautiful animation showing dirty energy transforming to clean energy
 */

class EnergyTransformation {
    constructor() {
        this.container = null;
        this.particles = [];
        this.isRunning = false;
        this.animationId = null;
        this.particleCount = 20;
        this.transformationRadius = 150;
        
        this.init();
    }

    init() {
        this.setupContainer();
        if (this.container) {
            this.createParticles();
            this.startAnimation();
            console.log('Energy Transformation Animation initialized');
        }
    }

    /**
     * Setup container element
     */
    setupContainer() {
        this.container = document.getElementById('energyTransformation');
        
        if (!this.container) {
            console.warn('Energy transformation container not found');
            return;
        }
    }

    /**
     * Create energy particles
     */
    createParticles() {
        if (!this.container) return;

        // Clear existing particles
        this.container.innerHTML = '';
        this.particles = [];

        for (let i = 0; i < this.particleCount; i++) {
            this.createParticle(i);
        }
    }

    /**
     * Create individual particle
     */
    createParticle(index) {
        const particle = document.createElement('div');
        particle.className = 'energy-particle dirty';
        
        // Random position around the circle
        const angle = (index / this.particleCount) * 2 * Math.PI;
        const radius = this.transformationRadius;
        const x = Math.cos(angle) * radius + this.transformationRadius;
        const y = Math.sin(angle) * radius + this.transformationRadius;
        
        particle.style.left = x + 'px';
        particle.style.top = y + 'px';
        
        // Random animation delay
        const delay = Math.random() * 3;
        particle.style.animationDelay = delay + 's';
        
        this.container.appendChild(particle);
        this.particles.push({
            element: particle,
            angle: angle,
            delay: delay,
            isDirty: true
        });

        // Transform to clean energy after delay
        setTimeout(() => {
            this.transformParticle(particle, index);
        }, (delay + 1.5) * 1000);
    }

    /**
     * Transform particle from dirty to clean
     */
    transformParticle(particle, index) {
        // Change to clean energy
        particle.classList.remove('dirty');
        particle.classList.add('clean');
        
        // Create transformation effect
        this.createTransformationEffect(particle);
        
        // Reset particle after animation cycle
        setTimeout(() => {
            if (particle.parentNode) {
                particle.classList.remove('clean');
                particle.classList.add('dirty');
                
                // Schedule next transformation
                setTimeout(() => {
                    this.transformParticle(particle, index);
                }, Math.random() * 2000 + 1000);
            }
        }, 3000);
    }

    /**
     * Create transformation effect
     */
    createTransformationEffect(particle) {
        // Create sparkle effect
        for (let i = 0; i < 3; i++) {
            const sparkle = document.createElement('div');
            sparkle.className = 'energy-sparkle';
            sparkle.style.position = 'absolute';
            sparkle.style.width = '2px';
            sparkle.style.height = '2px';
            sparkle.style.background = 'rgba(0, 255, 209, 0.8)';
            sparkle.style.borderRadius = '50%';
            sparkle.style.boxShadow = '0 0 8px rgba(0, 255, 209, 0.6)';
            sparkle.style.left = particle.style.left;
            sparkle.style.top = particle.style.top;
            sparkle.style.pointerEvents = 'none';
            sparkle.style.animation = 'sparkleEffect 0.5s ease-out forwards';
            
            // Random direction for sparkle
            const angle = Math.random() * 2 * Math.PI;
            const distance = Math.random() * 20 + 10;
            const endX = Math.cos(angle) * distance;
            const endY = Math.sin(angle) * distance;
            
            sparkle.style.setProperty('--end-x', endX + 'px');
            sparkle.style.setProperty('--end-y', endY + 'px');
            
            this.container.appendChild(sparkle);
            
            // Remove sparkle after animation
            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.remove();
                }
            }, 500);
        }
    }

    /**
     * Start animation loop
     */
    startAnimation() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.animateParticles();
    }

    /**
     * Animate particles continuously
     */
    animateParticles() {
        if (!this.isRunning) return;

        // Update particle positions for floating effect
        this.particles.forEach((particleData, index) => {
            const time = Date.now() * 0.001;
            const baseAngle = particleData.angle;
            const floatAngle = baseAngle + Math.sin(time + index) * 0.1;
            const floatRadius = this.transformationRadius + Math.sin(time * 0.5 + index) * 10;
            
            const x = Math.cos(floatAngle) * floatRadius + this.transformationRadius;
            const y = Math.sin(floatAngle) * floatRadius + this.transformationRadius;
            
            particleData.element.style.left = x + 'px';
            particleData.element.style.top = y + 'px';
        });

        this.animationId = requestAnimationFrame(() => this.animateParticles());
    }

    /**
     * Stop animation
     */
    stopAnimation() {
        this.isRunning = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    /**
     * Pause animation
     */
    pause() {
        this.stopAnimation();
    }

    /**
     * Resume animation
     */
    resume() {
        this.startAnimation();
    }

    /**
     * Reset animation
     */
    reset() {
        this.stopAnimation();
        this.createParticles();
        this.startAnimation();
    }

    /**
     * Destroy animation
     */
    destroy() {
        this.stopAnimation();
        
        if (this.container) {
            this.container.innerHTML = '';
        }
        
        this.particles = [];
        console.log('Energy Transformation Animation destroyed');
    }
}

// CSS for sparkle effects
const sparkleEffectCSS = `
@keyframes sparkleEffect {
    0% {
        opacity: 1;
        transform: translate(0, 0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(var(--end-x), var(--end-y)) scale(0);
    }
}

.energy-sparkle {
    z-index: 10;
}

.energy-transformation {
    z-index: 5;
}

/* Enhanced particle effects */
.energy-particle {
    transition: all 0.3s ease;
}

.energy-particle.dirty {
    filter: blur(0.5px);
}

.energy-particle.clean {
    filter: blur(0px);
}
`;

// Inject CSS
const styleSheet = document.createElement('style');
styleSheet.textContent = sparkleEffectCSS;
document.head.appendChild(styleSheet);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for hero section to be visible
    const heroSection = document.querySelector('#hero');
    if (heroSection) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        new EnergyTransformation();
                    }, 2000); // Start after hero loads
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        observer.observe(heroSection);
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnergyTransformation;
}
