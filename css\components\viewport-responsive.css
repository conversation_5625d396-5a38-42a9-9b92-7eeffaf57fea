/* ===================================
   VIEWPORT RESPONSIVE SYSTEM
   Professional responsive design for all devices
   =================================== */

/* Global Viewport Settings */
* {
    box-sizing: border-box !important;
}

html {
    overflow-x: hidden !important;
    scroll-behavior: smooth !important;
}

body {
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100vw !important;
    max-width: 100vw !important;
}

/* Container System */
.hero-container-professional,
.about-container,
.services-container-professional,
.projects-container-professional,
.activities-container,
.rebranding-container,
.contact-container {
    max-width: 100vw !important;
    width: 100% !important;
    margin: 0 auto !important;
    padding-left: clamp(20px, 5vw, 80px) !important;
    padding-right: clamp(20px, 5vw, 80px) !important;
    box-sizing: border-box !important;
}

/* Section Base Settings */
.hero-section-professional,
.about-section-ultimate,
.services-section-professional,
.projects-section-professional,
.activities-section-fixed,
.rebranding-section-ultimate,
.contact-section-fixed {
    width: 100vw !important;
    max-width: 100vw !important;
    min-height: 100vh !important;
    height: auto !important;
    overflow: hidden !important;
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    padding: clamp(60px, 8vh, 120px) 0 !important;
    box-sizing: border-box !important;
}

/* Hero Section Specific */
.hero-section-professional {
    padding-top: clamp(100px, 12vh, 140px) !important;
}

/* Grid Systems */
.services-grid-professional,
.projects-grid-professional,
.activities-grid,
.about-content-grid,
.rebranding-content-grid {
    display: grid !important;
    width: 100% !important;
    max-width: 100% !important;
    gap: clamp(20px, 3vw, 40px) !important;
    margin: clamp(40px, 6vh, 80px) 0 !important;
    box-sizing: border-box !important;
}

/* Desktop Large (1920px+) */
@media (min-width: 1920px) {

    .services-grid-professional,
    .projects-grid-professional {
        grid-template-columns: repeat(4, 1fr) !important;
    }

    .activities-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .about-content-grid,
    .rebranding-content-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* Desktop Standard (1200px - 1919px) */
@media (min-width: 1200px) and (max-width: 1919px) {

    .services-grid-professional,
    .projects-grid-professional {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .activities-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .about-content-grid,
    .rebranding-content-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* Desktop Small (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {

    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .about-content-grid,
    .rebranding-content-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {

    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid,
    .about-content-grid,
    .rebranding-content-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .hero-container-professional,
    .about-container,
    .services-container-professional,
    .projects-container-professional,
    .activities-container,
    .rebranding-container,
    .contact-container {
        padding-left: 30px !important;
        padding-right: 30px !important;
    }
}

/* Mobile Large (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {

    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid,
    .about-content-grid,
    .rebranding-content-grid {
        grid-template-columns: 1fr !important;
    }

    .hero-container-professional,
    .about-container,
    .services-container-professional,
    .projects-container-professional,
    .activities-container,
    .rebranding-container,
    .contact-container {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }
}

/* Mobile Small (320px - 575px) */
@media (max-width: 575px) {

    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid,
    .about-content-grid,
    .rebranding-content-grid {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }

    .hero-container-professional,
    .about-container,
    .services-container-professional,
    .projects-container-professional,
    .activities-container,
    .rebranding-container,
    .contact-container {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .hero-section-professional,
    .about-section-ultimate,
    .services-section-professional,
    .projects-section-professional,
    .activities-section-fixed,
    .rebranding-section-ultimate,
    .contact-section-fixed {
        padding: 80px 0 60px !important;
    }
}

/* Typography Responsive */
.section-title-professional,
.section-title,
.hero-title-professional {
    font-size: clamp(1.8rem, 4vw, 3.5rem) !important;
    line-height: 1.2 !important;
    margin-bottom: clamp(20px, 3vh, 40px) !important;
    text-align: center !important;
}

.section-description-professional,
.section-description,
.hero-description-professional {
    font-size: clamp(1rem, 2vw, 1.2rem) !important;
    line-height: 1.6 !important;
    margin-bottom: clamp(30px, 4vh, 50px) !important;
    text-align: center !important;
    max-width: 100% !important;
}

/* Card Responsive */
.service-card-professional,
.project-card-professional,
.activity-card,
.story-card,
.values-card {
    width: 100% !important;
    max-width: 100% !important;
    height: auto !important;
    min-height: auto !important;
    padding: clamp(20px, 3vw, 40px) !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

/* Stats Grid Responsive */
.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: clamp(15px, 3vw, 30px) !important;
    width: 100% !important;
    max-width: 600px !important;
    margin: clamp(30px, 4vh, 50px) auto !important;
}

@media (max-width: 576px) {
    .stats-grid {
        grid-template-columns: 1fr !important;
        max-width: 300px !important;
    }
}

/* Button Responsive */
.cta-buttons-container {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: clamp(15px, 2vw, 25px) !important;
    justify-content: center !important;
    align-items: center !important;
    margin: clamp(30px, 4vh, 50px) 0 !important;
}

@media (max-width: 576px) {
    .cta-buttons-container {
        flex-direction: column !important;
        width: 100% !important;
    }

    .cta-primary-advanced,
    .cta-secondary-advanced {
        width: 100% !important;
        max-width: 280px !important;
        justify-content: center !important;
    }
}

/* Logo Responsive */
.hero-logo-main {
    width: clamp(80px, 8vw, 120px) !important;
    height: auto !important;
    max-width: 100% !important;
}

/* Prevent Horizontal Scroll */
.hero-background-system,
.about-background,
.services-background-system,
.projects-background-system,
.activities-background,
.rebranding-background,
.contact-background {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow: hidden !important;
}

/* Advanced Responsive Typography */
.hero-title-professional .title-line-1 {
    font-size: clamp(2rem, 6vw, 4rem) !important;
}

.hero-title-professional .title-line-2 {
    font-size: clamp(2.2rem, 6.5vw, 4.5rem) !important;
}

.hero-title-professional .title-line-3 {
    font-size: clamp(1.5rem, 4vw, 2.8rem) !important;
}

/* Service Cards Responsive */
.service-card-professional {
    min-height: clamp(300px, 40vh, 450px) !important;
    padding: clamp(20px, 4vw, 40px) !important;
}

/* Project Cards Responsive */
.project-card-professional {
    min-height: clamp(350px, 45vh, 500px) !important;
    padding: clamp(20px, 4vw, 40px) !important;
}

/* Activity Cards Responsive */
.activity-card {
    min-height: clamp(280px, 35vh, 400px) !important;
    padding: clamp(20px, 4vw, 35px) !important;
}

/* Contact Form Responsive */
.contact-form {
    padding: clamp(30px, 5vw, 50px) !important;
}

.contact-form input,
.contact-form textarea {
    padding: clamp(12px, 2vw, 18px) !important;
    font-size: clamp(0.9rem, 1.5vw, 1.1rem) !important;
}

/* Header Responsive */
.site-header {
    padding: clamp(10px, 2vh, 20px) 0 !important;
}

.header-container {
    padding: 0 clamp(20px, 5vw, 80px) !important;
}

/* Footer Responsive */
.site-footer {
    padding: clamp(40px, 8vh, 80px) clamp(20px, 5vw, 80px) !important;
}

/* Ultra-wide Screens (2560px+) */
@media (min-width: 2560px) {

    .hero-container-professional,
    .about-container,
    .services-container-professional,
    .projects-container-professional,
    .activities-container,
    .rebranding-container,
    .contact-container {
        max-width: 2200px !important;
        padding-left: 100px !important;
        padding-right: 100px !important;
    }

    .services-grid-professional,
    .projects-grid-professional {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 50px !important;
    }
}

/* Very Small Mobile (320px and below) */
@media (max-width: 320px) {

    .hero-container-professional,
    .about-container,
    .services-container-professional,
    .projects-container-professional,
    .activities-container,
    .rebranding-container,
    .contact-container {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    .hero-section-professional,
    .about-section-ultimate,
    .services-section-professional,
    .projects-section-professional,
    .activities-section-fixed,
    .rebranding-section-ultimate,
    .contact-section-fixed {
        padding: 70px 0 50px !important;
    }

    .service-card-professional,
    .project-card-professional,
    .activity-card {
        padding: 15px !important;
        min-height: 250px !important;
    }
}

/* Landscape Mobile Optimization */
@media (max-height: 500px) and (orientation: landscape) {

    .hero-section-professional,
    .about-section-ultimate,
    .services-section-professional,
    .projects-section-professional,
    .activities-section-fixed,
    .rebranding-section-ultimate,
    .contact-section-fixed {
        min-height: auto !important;
        padding: 40px 0 !important;
    }

    .hero-container-professional {
        padding-top: 30px !important;
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 15px !important;
    }
}

/* Print Styles */
@media print {

    .hero-background-system,
    .about-background,
    .services-background-system,
    .projects-background-system,
    .activities-background,
    .rebranding-background,
    .contact-background {
        display: none !important;
    }

    .site-header,
    .site-footer {
        position: static !important;
    }
}