/**
 * INOVA Energy - Hero Stats Persian Numbers Animation
 * Professional stats counter with Persian number support
 */

class HeroStatsPersian {
    constructor() {
        this.statsSection = null;
        this.statNumbers = [];
        this.isAnimated = false;
        
        this.init();
    }

    init() {
        this.setupStatsSection();
        this.setupObserver();
        console.log('Hero Stats Persian initialized');
    }

    /**
     * Setup stats section and elements
     */
    setupStatsSection() {
        this.statsSection = document.querySelector('.hero-stats-ultimate');
        if (!this.statsSection) {
            console.warn('Stats section not found');
            return;
        }

        this.statNumbers = this.statsSection.querySelectorAll('.stat-number-ultimate');
        console.log(`Found ${this.statNumbers.length} stat numbers`);
    }

    /**
     * Setup intersection observer for animation trigger
     */
    setupObserver() {
        if (!this.statsSection) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.isAnimated) {
                    this.animateStatsCounter();
                    this.isAnimated = true;
                    observer.unobserve(entry.target);
                }
            });
        }, { 
            threshold: 0.3,
            rootMargin: '0px 0px -100px 0px'
        });

        observer.observe(this.statsSection);
    }

    /**
     * Animate stats counter with Persian numbers
     */
    animateStatsCounter() {
        this.statNumbers.forEach((element, index) => {
            const targetValue = parseInt(element.getAttribute('data-count'));
            const persianTarget = element.getAttribute('data-persian');
            const duration = 2000 + (index * 300); // Stagger animation
            
            // Add animation class
            element.parentElement.parentElement.classList.add('stat-animating');
            
            setTimeout(() => {
                this.animateNumber(element, 0, targetValue, duration, persianTarget);
            }, index * 200);
        });
    }

    /**
     * Animate number counting with easing and Persian numbers
     */
    animateNumber(element, start, end, duration, persianTarget) {
        const startTime = performance.now();
        
        const updateNumber = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function for smooth animation
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const currentValue = Math.floor(start + (end - start) * easeOutCubic);
            
            // Convert to Persian numbers
            element.textContent = this.convertToPersianNumbers(currentValue);
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                element.textContent = persianTarget || this.convertToPersianNumbers(end);
                
                // Remove animation class
                element.parentElement.parentElement.classList.remove('stat-animating');
                element.parentElement.parentElement.classList.add('stat-completed');
            }
        };
        
        requestAnimationFrame(updateNumber);
    }

    /**
     * Convert English numbers to Persian numbers
     */
    convertToPersianNumbers(num) {
        const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        return num.toString().replace(/\d/g, (digit) => persianDigits[parseInt(digit)]);
    }

    /**
     * Convert Persian numbers to English numbers
     */
    convertToEnglishNumbers(persianNum) {
        const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        
        let result = persianNum.toString();
        persianDigits.forEach((persian, index) => {
            result = result.replace(new RegExp(persian, 'g'), englishDigits[index]);
        });
        
        return result;
    }

    /**
     * Format number with Persian separators
     */
    formatPersianNumber(num) {
        const persianNum = this.convertToPersianNumbers(num);
        // Add Persian thousand separator if needed
        return persianNum.replace(/\B(?=(\d{3})+(?!\d))/g, '،');
    }

    /**
     * Reset animation (for testing)
     */
    resetAnimation() {
        this.isAnimated = false;
        this.statNumbers.forEach(element => {
            element.textContent = '۰';
            element.parentElement.parentElement.classList.remove('stat-animating', 'stat-completed');
        });
        
        this.setupObserver();
    }

    /**
     * Manually trigger animation (for testing)
     */
    triggerAnimation() {
        if (!this.isAnimated) {
            this.animateStatsCounter();
            this.isAnimated = true;
        }
    }

    /**
     * Destroy stats animation
     */
    destroy() {
        // Clean up observers and event listeners
        console.log('Hero Stats Persian destroyed');
    }
}

// CSS for animation states
const statsAnimationCSS = `
.stat-animating {
    animation: statPulse 0.5s ease-in-out;
}

.stat-completed {
    animation: statComplete 0.3s ease-out;
}

@keyframes statPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes statComplete {
    0% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.stat-number-ultimate {
    font-family: 'IRANSans', 'Tahoma', sans-serif;
    font-weight: 700;
    font-feature-settings: 'tnum';
    direction: ltr;
    unicode-bidi: bidi-override;
}
`;

// Inject CSS
const styleSheet = document.createElement('style');
styleSheet.textContent = statsAnimationCSS;
document.head.appendChild(styleSheet);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new HeroStatsPersian();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeroStatsPersian;
}
