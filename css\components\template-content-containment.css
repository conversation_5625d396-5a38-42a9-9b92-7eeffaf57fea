/* ===================================
   TEMPLATE CONTENT CONTAINMENT SYSTEM
   Ensures all template content stays within section boundaries
   =================================== */

/* ===================================
   GLOBAL CONTAINMENT CLASSES
   =================================== */

/* Content Containers */
.hero-content-contained,
.services-content-contained,
.about-content-contained,
.projects-content-contained,
.activities-content-contained,
.contact-content-contained,
.rebranding-content-contained {
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    padding: clamp(20px, 4vh, 60px) clamp(20px, 5vw, 80px) !important;
}

/* Section Items */
.hero-section-item,
.service-item-contained,
.section-contained,
.project-item-contained,
.activity-item-contained,
.contact-item-contained {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
}

/* Headers */
.header-contained {
    width: 100% !important;
    max-width: 100% !important;
    text-align: center !important;
    margin: 0 0 clamp(20px, 4vh, 40px) 0 !important;
    overflow: hidden !important;
    flex-shrink: 0 !important;
}

/* Titles */
.title-contained {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    text-align: center !important;
    line-height: 1.1 !important;
}

.title-responsive {
    font-size: clamp(1.2rem, 4vw, 2.5rem) !important;
    line-height: 1.1 !important;
    margin: 0 !important;
    padding: 0 !important;
    display: block !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* Descriptions */
.description-contained {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
    text-overflow: ellipsis !important;
    font-size: clamp(0.9rem, 2vw, 1.1rem) !important;
    line-height: 1.5 !important;
    text-align: center !important;
}

/* Cards */
.card-contained {
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    padding: clamp(15px, 3vh, 25px) !important;
    margin: 0 !important;
}

.content-contained {
    flex: 1 !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Grids */
.services-grid-contained,
.content-grid-contained,
.projects-grid-contained,
.activities-grid-contained {
    width: 100% !important;
    max-width: 100% !important;
    height: auto !important;
    max-height: 60vh !important;
    overflow: hidden !important;
    display: grid !important;
    gap: clamp(15px, 2.5vh, 25px) !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

/* Stats */
.stats-contained {
    width: 100% !important;
    max-width: 600px !important;
    margin: 0 auto !important;
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: clamp(10px, 2vw, 20px) !important;
    overflow: hidden !important;
}

.stat-contained {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
    text-align: center !important;
    padding: clamp(8px, 1.5vh, 15px) !important;
    box-sizing: border-box !important;
}

/* CTA Buttons */
.cta-contained {
    width: 100% !important;
    max-width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: clamp(10px, 2vw, 20px) !important;
    flex-wrap: wrap !important;
    margin: 0 !important;
    overflow: hidden !important;
}

/* Logo */
.logo-contained {
    width: clamp(80px, 12vw, 150px) !important;
    height: clamp(80px, 12vw, 150px) !important;
    max-width: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto !important;
}

.logo-responsive {
    width: 100% !important;
    height: auto !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
}

/* Floating Elements */
.floating-contained {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    overflow: hidden !important;
    pointer-events: none !important;
    z-index: 1 !important;
}

.element-contained {
    position: absolute !important;
    max-width: 100px !important;
    max-height: 100px !important;
    overflow: hidden !important;
}

.shapes-contained {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    overflow: hidden !important;
    pointer-events: none !important;
}

/* Scroll Indicator */
.scroll-contained {
    position: absolute !important;
    bottom: clamp(20px, 4vh, 40px) !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 15 !important;
    overflow: hidden !important;
    max-width: 200px !important;
    text-align: center !important;
}

/* ===================================
   RESPONSIVE CONTAINMENT
   =================================== */

/* Desktop Large (1920px+) */
@media (min-width: 1920px) {
    .services-grid-contained,
    .projects-grid-contained {
        grid-template-columns: repeat(4, 1fr) !important;
        max-height: 50vh !important;
    }
    
    .content-grid-contained,
    .activities-grid-contained {
        grid-template-columns: repeat(3, 1fr) !important;
        max-height: 55vh !important;
    }
}

/* Desktop Standard (1200px - 1919px) */
@media (min-width: 1200px) and (max-width: 1919px) {
    .services-grid-contained,
    .projects-grid-contained {
        grid-template-columns: repeat(3, 1fr) !important;
        max-height: 55vh !important;
    }
    
    .content-grid-contained,
    .activities-grid-contained {
        grid-template-columns: repeat(3, 1fr) !important;
        max-height: 60vh !important;
    }
}

/* Desktop Small (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .services-grid-contained,
    .projects-grid-contained,
    .activities-grid-contained {
        grid-template-columns: repeat(2, 1fr) !important;
        max-height: 65vh !important;
    }
    
    .content-grid-contained {
        grid-template-columns: repeat(2, 1fr) !important;
        max-height: 65vh !important;
    }
}

/* Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .services-grid-contained,
    .projects-grid-contained,
    .activities-grid-contained {
        grid-template-columns: repeat(2, 1fr) !important;
        max-height: 60vh !important;
    }
    
    .content-grid-contained {
        grid-template-columns: 1fr !important;
        max-height: 65vh !important;
    }
    
    .description-contained {
        -webkit-line-clamp: 2 !important;
    }
    
    .stats-contained {
        grid-template-columns: 1fr !important;
        gap: 8px !important;
    }
}

/* Mobile Large (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .services-grid-contained,
    .projects-grid-contained,
    .activities-grid-contained,
    .content-grid-contained {
        grid-template-columns: 1fr !important;
        max-height: 65vh !important;
        overflow-y: auto !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
    }
    
    .services-grid-contained::-webkit-scrollbar,
    .projects-grid-contained::-webkit-scrollbar,
    .activities-grid-contained::-webkit-scrollbar,
    .content-grid-contained::-webkit-scrollbar {
        display: none !important;
    }
    
    .cta-contained {
        flex-direction: column !important;
        gap: 10px !important;
    }
}

/* Mobile Small (320px - 575px) */
@media (max-width: 575px) {
    .hero-content-contained,
    .services-content-contained,
    .about-content-contained,
    .projects-content-contained,
    .activities-content-contained,
    .contact-content-contained,
    .rebranding-content-contained {
        padding: clamp(70px, 10vh, 90px) 15px clamp(15px, 2vh, 25px) !important;
    }
    
    .services-grid-contained,
    .projects-grid-contained,
    .activities-grid-contained,
    .content-grid-contained {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
        max-height: 60vh !important;
        overflow-y: auto !important;
    }
    
    .card-contained {
        padding: 15px !important;
    }
    
    .description-contained {
        -webkit-line-clamp: 2 !important;
    }
    
    .title-responsive {
        white-space: normal !important;
        text-align: center !important;
    }
    
    .stats-contained {
        grid-template-columns: 1fr !important;
        gap: 8px !important;
    }
    
    .cta-contained {
        flex-direction: column !important;
        gap: 10px !important;
    }
}
