class Stats {
    constructor() {
        this.stats = document.querySelectorAll('.stat-number, .stat-value, [data-count], [data-value]');
        this.observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        };
        this.init();
    }

    init() {
        // Use Intersection Observer to trigger animations when elements come into view
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(this.handleIntersection.bind(this), this.observerOptions);
            this.stats.forEach(stat => {
                this.observer.observe(stat);
            });
        } else {
            // Fallback for older browsers
            this.stats.forEach(stat => this.animateStat(stat));
        }
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                this.animateStat(entry.target);
                entry.target.classList.add('animated');
                this.observer.unobserve(entry.target);
            }
        });
    }

    animateStat(stat) {
        // Get target value from various possible attributes and content
        let target = this.getTargetValue(stat);

        if (target === null || isNaN(target) || target <= 0) {
            console.warn('No valid number found for stat element:', stat, 'Target:', target);
            // Don't animate, just keep original content
            return;
        }

        // Store original content to preserve formatting
        const originalText = stat.textContent.trim();
        const prefix = this.extractPrefix(originalText);
        const suffix = this.extractSuffix(originalText);

        // Ensure we have valid prefix and suffix
        const safePrefix = prefix || '';
        const safeSuffix = suffix || '';

        // Set initial value
        stat.textContent = safePrefix + '0' + safeSuffix;

        // Animate the value with validation
        this.animateValue(stat, 0, target, 2000, safePrefix, safeSuffix);
    }

    getTargetValue(element) {
        // Try different attributes and methods to get the target value
        const dataValue = element.getAttribute('data-value');
        const dataCount = element.getAttribute('data-count');
        const textContent = element.textContent.trim();

        // Priority order: data-value, data-count, text content
        if (dataValue && dataValue !== '' && !isNaN(parseFloat(dataValue))) {
            const parsed = parseInt(dataValue, 10);
            return isNaN(parsed) ? null : parsed;
        }

        if (dataCount && dataCount !== '' && !isNaN(parseFloat(dataCount))) {
            const parsed = parseInt(dataCount, 10);
            return isNaN(parsed) ? null : parsed;
        }

        // Extract number from text content - improved regex to handle various formats
        const numberMatch = textContent.match(/(\d+(?:\.\d+)?)/);
        if (numberMatch && numberMatch[1]) {
            const parsed = parseInt(numberMatch[1], 10);
            return isNaN(parsed) ? null : parsed;
        }

        // Fallback: try to find any digits in the text
        const digitsOnly = textContent.replace(/\D/g, '');
        if (digitsOnly && digitsOnly.length > 0) {
            const parsed = parseInt(digitsOnly, 10);
            return isNaN(parsed) ? null : parsed;
        }

        return null;
    }

    extractPrefix(text) {
        const match = text.match(/^([^\d]*)/);
        return match ? match[1] : '';
    }

    extractSuffix(text) {
        const match = text.match(/\d+(.*)$/);
        return match ? match[1] : '';
    }

    animateValue(element, start, end, duration, prefix = '', suffix = '') {
        // Ensure we have valid numbers
        if (isNaN(start) || isNaN(end) || start < 0 || end < 0) {
            console.warn('Invalid animation values:', { start, end, element });
            element.textContent = prefix + (isNaN(end) ? '0' : end) + suffix;
            return;
        }

        // Ensure prefix and suffix are strings
        const safePrefix = String(prefix || '');
        const safeSuffix = String(suffix || '');

        let startTimestamp = null;
        const step = (timestamp) => {
            if (!startTimestamp) startTimestamp = timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);

            // Use easing function for smoother animation
            const easedProgress = this.easeOutQuart(progress);
            const current = Math.floor(easedProgress * (end - start) + start);

            // Ensure current is a valid number
            if (!isNaN(current) && current >= 0) {
                element.textContent = safePrefix + current + safeSuffix;
            } else {
                // Fallback to safe value
                element.textContent = safePrefix + '0' + safeSuffix;
            }

            if (progress < 1) {
                window.requestAnimationFrame(step);
            } else {
                // Ensure final value is exact and valid
                const finalValue = isNaN(end) ? 0 : end;
                element.textContent = safePrefix + finalValue + safeSuffix;
            }
        };
        window.requestAnimationFrame(step);
    }

    // Easing function for smoother animation
    easeOutQuart(t) {
        return 1 - (--t) * t * t * t;
    }

    // Method to manually trigger animation for specific elements
    triggerAnimation(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.classList.remove('animated');
            this.animateStat(element);
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.statsInstance = new Stats();
});

// Also initialize when page is fully loaded (for dynamic content)
window.addEventListener('load', () => {
    if (!window.statsInstance) {
        window.statsInstance = new Stats();
    }
});

// Reinitialize stats when language changes
document.addEventListener('languageChanged', () => {
    setTimeout(() => {
        if (window.statsInstance) {
            window.statsInstance.triggerAnimation('.stat-number, .stat-value, [data-count], [data-value]');
        }
    }, 100);
});