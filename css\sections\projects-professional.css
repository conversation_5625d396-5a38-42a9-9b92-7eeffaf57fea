/* ===================================
   PROJECTS SECTION - PROFESSIONAL DESIGN
   Advanced styling with cutting-edge animations
   =================================== */

/* Projects Section Base */
.projects-section-professional {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg, 
        rgba(0, 15, 30, 1) 0%, 
        rgba(0, 31, 63, 1) 50%,
        rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
}

/* RTL/LTR Support */
.rtl .projects-section-professional {
    direction: rtl;
    text-align: right;
}

.ltr .projects-section-professional {
    direction: ltr;
    text-align: left;
}

/* Background System */
.projects-background-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient-advanced {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, 
        rgba(0, 163, 255, 0.1) 0%, 
        rgba(0, 255, 209, 0.05) 50%, 
        transparent 70%);
    animation: gradientPulse 8s ease-in-out infinite;
}

.bg-pattern-advanced {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 255, 209, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 209, 0.05) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 30s linear infinite;
    opacity: 0.3;
}

/* Floating Shapes */
.floating-shapes-advanced {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape-advanced {
    position: absolute;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, 
        rgba(0, 255, 209, 0.1), 
        rgba(0, 163, 255, 0.1));
    border-radius: 50%;
    animation: floatingMove 12s ease-in-out infinite;
}

.shape-advanced.shape-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-advanced.shape-2 {
    top: 60%;
    right: 15%;
    animation-delay: 4s;
    width: 80px;
    height: 80px;
}

.shape-advanced.shape-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 8s;
    width: 60px;
    height: 60px;
}

/* Projects Container */
.projects-container-professional {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.projects-header {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 20px;
    background: rgba(0, 255, 209, 0.1);
    border: 1px solid rgba(0, 255, 209, 0.3);
    border-radius: 50px;
    color: var(--accent);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.section-title-professional {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 30px;
    color: var(--text-light);
}

.title-line-1,
.title-line-2,
.title-line-3 {
    display: block;
    opacity: 0;
    transform: translateY(30px);
    animation: titleSlideIn 1s ease-out forwards;
}

.title-line-1 {
    font-size: 1em;
    margin-bottom: 10px;
    animation-delay: 0.5s;
}

.title-line-2 {
    font-size: 1.2em;
    margin-bottom: 10px;
    animation-delay: 0.8s;
}

.title-line-3 {
    font-size: 0.7em;
    font-weight: 500;
    animation-delay: 1.1s;
}

.gradient-text-professional {
    background: linear-gradient(135deg, 
        var(--primary) 0%, 
        var(--accent) 50%, 
        var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-description-professional {
    font-size: clamp(1.1rem, 2.5vw, 1.3rem);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    max-width: 700px;
    margin: 0 auto;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 1s ease-out 1.4s forwards;
}

/* Projects Grid */
.projects-grid-professional {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    margin: 80px 0;
}

/* Project Item */
.project-item-professional {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease-out forwards;
}

.project-item-professional:nth-child(1) { animation-delay: 1.6s; }
.project-item-professional:nth-child(2) { animation-delay: 1.8s; }
.project-item-professional:nth-child(3) { animation-delay: 2s; }

/* Project Card */
.project-card-professional {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    overflow: hidden;
}

.project-card-professional:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: rgba(0, 255, 209, 0.3);
    box-shadow: 0 20px 60px rgba(0, 163, 255, 0.2);
}

/* Project Image */
.project-image-container {
    position: relative;
    height: 250px;
    background: linear-gradient(135deg, 
        rgba(0, 163, 255, 0.1), 
        rgba(0, 255, 209, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--accent);
}

.image-icon svg {
    width: 40px;
    height: 40px;
}

/* Project Overlay */
.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(0, 15, 30, 0.9), 
        rgba(0, 31, 63, 0.9));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30px;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.project-card-professional:hover .project-overlay {
    opacity: 1;
}

/* Animations */
@keyframes gradientPulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.05); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes floatingMove {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(10px, -10px) rotate(90deg); }
    50% { transform: translate(-5px, -20px) rotate(180deg); }
    75% { transform: translate(-10px, -5px) rotate(270deg); }
}

@keyframes titleSlideIn {
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .projects-grid-professional {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .projects-container-professional {
        padding: 0 20px;
    }
}
