/* ===================================
   HERO CONTENT ULTIMATE DESIGN
   Professional content animations and layouts
   =================================== */

/* ===================================
   HERO CONTENT CONTAINER
   =================================== */

.hero-content-ultimate {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 clamp(20px, 5vw, 80px);
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    justify-content: center;
    gap: clamp(40px, 8vw, 100px);
    height: 100vh;
}

/* ===================================
   HERO LEFT SIDE - LOGO SECTION
   =================================== */

.hero-logo-ultimate {
    position: relative;
    z-index: 10;
    grid-column: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* ===================================
   HERO RIGHT SIDE - CONTENT SECTION
   =================================== */

.hero-main-content-ultimate {
    grid-column: 2;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: clamp(25px, 5vh, 40px);
    text-align: left;
    height: 100%;
}

/* ===================================
   HERO TITLE ULTIMATE
   =================================== */

.hero-title-ultimate {
    position: relative;
    margin-bottom: clamp(20px, 4vh, 40px);
}

.hero-title-main-ultimate {
    margin: 0;
    padding: 0;
    line-height: 1.1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: clamp(5px, 1vh, 15px);
}

.title-word {
    position: relative;
    display: inline-block;
    overflow: hidden;
}

.word-content {
    display: inline-block;
    position: relative;
    z-index: 2;
}

.title-word.word-1 .word-content.company-name {
    display: flex;
    align-items: baseline;
    gap: clamp(10px, 2vw, 20px);
    font-size: clamp(3rem, 10vw, 5rem);
    font-weight: 800;
    letter-spacing: 0.02em;
}

.brand-inova {
    background: linear-gradient(135deg, #00a3ff 0%, #0080ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Arial Black', 'Helvetica', sans-serif;
    text-shadow: 0 0 30px rgba(0, 163, 255, 0.3);
}

.brand-energy {
    background: linear-gradient(135deg, #00ffd1 0%, #00e6bc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Arial Black', 'Helvetica', sans-serif;
    text-shadow: 0 0 30px rgba(0, 255, 209, 0.3);
}

.title-word.word-2 .word-content.typing-container {
    font-size: clamp(1.8rem, 6vw, 2.8rem);
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    letter-spacing: 0.03em;
    min-height: 1.2em;
    display: flex;
    align-items: center;
}

.typing-text {
    color: rgba(255, 255, 255, 0.9);
}

.typing-cursor {
    color: #00ffd1;
    animation: cursorBlink 1s infinite;
    margin-left: 2px;
}

.title-word.word-3 .word-content {
    font-size: clamp(1.2rem, 4vw, 2rem);
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.word-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse,
            rgba(0, 255, 209, 0.3) 0%,
            transparent 70%);
    z-index: 1;
    animation: wordGlow 3s ease-in-out infinite;
}

/* Title Decorations */
.title-decorations {
    position: relative;
    margin-top: clamp(15px, 3vh, 30px);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.title-underline-ultimate {
    width: clamp(100px, 20vw, 200px);
    height: 3px;
    background: linear-gradient(90deg,
            transparent 0%,
            #00ffd1 20%,
            #00a3ff 50%,
            #00ffd1 80%,
            transparent 100%);
    border-radius: 2px;
    animation: underlineGlow 2s ease-in-out infinite;
}

.title-particles {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 40px;
}

.title-particles .particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #00ffd1;
    border-radius: 50%;
    animation: titleParticleFloat 4s ease-in-out infinite;
}

.title-particles .particle:nth-child(1) {
    left: 20%;
    animation-delay: 0s;
}

.title-particles .particle:nth-child(2) {
    left: 50%;
    animation-delay: 1.3s;
}

.title-particles .particle:nth-child(3) {
    left: 80%;
    animation-delay: 2.6s;
}

/* ===================================
   HERO DESCRIPTION ULTIMATE
   =================================== */

.hero-description-ultimate {
    position: relative;
    max-width: 100%;
    margin: 0;
}

.description-text-ultimate {
    font-size: clamp(1rem, 2.5vw, 1.3rem);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.85);
    margin: 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.description-highlight {
    color: #00ffd1;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 255, 209, 0.5);
}

.description-accent {
    color: #00a3ff;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 163, 255, 0.5);
}

.description-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: clamp(20px, 4vh, 30px);
    gap: 15px;
}

.decoration-line {
    width: clamp(30px, 8vw, 60px);
    height: 1px;
    background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
}

.decoration-dot {
    width: 6px;
    height: 6px;
    background: #00ffd1;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(0, 255, 209, 0.8);
    animation: decorationDotPulse 2s ease-in-out infinite;
}

/* ===================================
   HERO STATS ULTIMATE
   =================================== */

.hero-stats-ultimate {
    position: relative;
    width: 100%;
    max-width: 100%;
    margin: 0;
}

.stats-container-ultimate {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: clamp(15px, 3vw, 25px);
    width: 100%;
    max-width: 500px;
}

.stat-item-ultimate {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: clamp(15px, 3vh, 20px);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    aspect-ratio: 1;
    justify-content: center;
    gap: clamp(8px, 1.5vh, 12px);
}

.stat-item-ultimate:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 255, 209, 0.3);
    box-shadow: 0 8px 25px rgba(0, 255, 209, 0.15);
}

.stat-icon-ultimate {
    width: clamp(24px, 4vw, 32px);
    height: clamp(24px, 4vw, 32px);
    color: #00ffd1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 209, 0.1);
    border-radius: 8px;
    padding: 6px;
    transition: all 0.3s ease;
}

.stat-icon-ultimate svg {
    width: 100%;
    height: 100%;
}

.stat-item-ultimate:hover .stat-icon-ultimate {
    background: rgba(0, 255, 209, 0.2);
    color: #ffffff;
    transform: scale(1.1);
}

.stat-content {
    text-align: center;
}

.stat-number-ultimate {
    font-size: clamp(1.5rem, 4vw, 2.2rem);
    font-weight: 700;
    color: #00ffd1;
    line-height: 1;
    text-shadow: 0 0 20px rgba(0, 255, 209, 0.5);
}

.stat-plus {
    display: inline;
    color: #00a3ff;
    font-size: 0.8em;
}

.stat-label-ultimate {
    font-size: clamp(0.8rem, 2vw, 1rem);
    color: rgba(255, 255, 255, 0.8);
    margin-top: 5px;
    line-height: 1.2;
}

/* ===================================
   HERO CTA ULTIMATE
   =================================== */

.hero-cta-ultimate {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    margin: 0;
}

.cta-container-ultimate {
    display: flex;
    gap: clamp(15px, 3vw, 25px);
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
}

.cta-button-ultimate {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: clamp(12px, 2.5vh, 18px) clamp(25px, 5vw, 35px);
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: clamp(0.9rem, 2vw, 1.1rem);
    transition: all 0.3s ease;
    overflow: hidden;
    min-width: clamp(140px, 25vw, 180px);
    backdrop-filter: blur(20px);
}

.cta-primary-ultimate {
    background: linear-gradient(135deg, #00a3ff 0%, #00ffd1 100%);
    color: #000;
    border: none;
    box-shadow: 0 8px 25px rgba(0, 255, 209, 0.3);
}

.cta-primary-ultimate:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 255, 209, 0.5);
}

.cta-secondary-ultimate {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.cta-secondary-ultimate:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(0, 255, 209, 0.5);
    box-shadow: 0 8px 25px rgba(0, 255, 209, 0.2);
}

.button-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.button-content-ultimate {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.button-icon-ultimate {
    width: 18px;
    height: 18px;
    transition: transform 0.3s ease;
}

.cta-button-ultimate:hover .button-icon-ultimate {
    transform: translateX(3px);
}

.button-ripple-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
    z-index: 1;
}

.cta-button-ultimate:active .button-ripple-effect {
    width: 300px;
    height: 300px;
}

/* ===================================
   CONTENT ANIMATIONS
   =================================== */

@keyframes wordGlow {

    0%,
    100% {
        opacity: 0.3;
    }

    50% {
        opacity: 0.6;
    }
}

@keyframes underlineGlow {

    0%,
    100% {
        opacity: 0.8;
        transform: scaleX(1);
    }

    50% {
        opacity: 1;
        transform: scaleX(1.1);
    }
}

@keyframes titleParticleFloat {

    0%,
    100% {
        transform: translateY(0) scale(1);
        opacity: 0.6;
    }

    25% {
        transform: translateY(-15px) scale(1.2);
        opacity: 1;
    }

    50% {
        transform: translateY(-5px) scale(0.8);
        opacity: 0.8;
    }

    75% {
        transform: translateY(-20px) scale(1.1);
        opacity: 1;
    }
}

@keyframes decorationDotPulse {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(0, 255, 209, 0.8);
    }

    50% {
        transform: scale(1.3);
        box-shadow: 0 0 20px rgba(0, 255, 209, 1);
    }
}

@keyframes statCircleRotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* ===================================
   ENTRANCE ANIMATIONS
   =================================== */

/* Title Cascade Animation */
.hero-title-ultimate[data-animate="title-cascade"] .title-word {
    opacity: 0;
    transform: translateY(50px);
    animation: titleWordSlideUp 1s ease-out forwards;
}

.hero-title-ultimate[data-animate="title-cascade"] .title-word.word-1 {
    animation-delay: 0.2s;
}

.hero-title-ultimate[data-animate="title-cascade"] .title-word.word-2 {
    animation-delay: 0.4s;
}

.hero-title-ultimate[data-animate="title-cascade"] .title-word.word-3 {
    animation-delay: 0.6s;
}

@keyframes titleWordSlideUp {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.8);
    }

    60% {
        opacity: 0.8;
        transform: translateY(-10px) scale(1.05);
    }

    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Description Fade In */
.hero-description-ultimate[data-animate="description-fade-in"] {
    opacity: 0;
    transform: translateY(30px);
    animation: descriptionFadeIn 1s ease-out forwards;
}

@keyframes descriptionFadeIn {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stats Counter Animation */
.hero-stats-ultimate[data-animate="stats-counter"] .stat-item-ultimate {
    opacity: 0;
    transform: translateY(40px) scale(0.8);
    animation: statItemReveal 0.8s ease-out forwards;
}

.hero-stats-ultimate[data-animate="stats-counter"] .stat-item-ultimate:nth-child(1) {
    animation-delay: 1.2s;
}

.hero-stats-ultimate[data-animate="stats-counter"] .stat-item-ultimate:nth-child(2) {
    animation-delay: 1.4s;
}

.hero-stats-ultimate[data-animate="stats-counter"] .stat-item-ultimate:nth-child(3) {
    animation-delay: 1.6s;
}

@keyframes statItemReveal {
    0% {
        opacity: 0;
        transform: translateY(40px) scale(0.8);
    }

    60% {
        opacity: 0.8;
        transform: translateY(-5px) scale(1.05);
    }

    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* CTA Entrance Animation */
.hero-cta-ultimate[data-animate="cta-entrance"] {
    opacity: 0;
    transform: translateY(30px);
    animation: ctaEntrance 1s ease-out forwards;
}

@keyframes ctaEntrance {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }

    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ===================================
   FLOATING ELEMENTS
   =================================== */

.hero-floating-ultimate {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    pointer-events: none;
}

.floating-element-ultimate {
    position: absolute;
    width: 20px;
    height: 20px;
}

.floating-element-ultimate.element-1 {
    top: 20%;
    left: 10%;
    animation: floatGentle 8s ease-in-out infinite;
}

.floating-element-ultimate.element-2 {
    top: 60%;
    right: 15%;
    animation: floatMedium 10s ease-in-out infinite;
}

.floating-element-ultimate.element-3 {
    bottom: 30%;
    left: 20%;
    animation: floatDynamic 6s ease-in-out infinite;
}

.floating-element-ultimate.element-4 {
    top: 40%;
    right: 25%;
    animation: floatSubtle 12s ease-in-out infinite;
}

.element-core {
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, #00ffd1 0%, transparent 70%);
    border-radius: 50%;
}

.element-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 255, 209, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: elementGlow 4s ease-in-out infinite;
}

.element-trail {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 30px;
    background: linear-gradient(to bottom, #00ffd1, transparent);
    transform: translate(-50%, -50%);
    opacity: 0.6;
}

@keyframes floatGentle {

    0%,
    100% {
        transform: translateY(0) translateX(0) rotate(0deg);
    }

    25% {
        transform: translateY(-20px) translateX(10px) rotate(90deg);
    }

    50% {
        transform: translateY(-10px) translateX(-15px) rotate(180deg);
    }

    75% {
        transform: translateY(-30px) translateX(5px) rotate(270deg);
    }
}

@keyframes floatMedium {

    0%,
    100% {
        transform: translateY(0) translateX(0) scale(1);
    }

    33% {
        transform: translateY(-25px) translateX(15px) scale(1.2);
    }

    66% {
        transform: translateY(-15px) translateX(-20px) scale(0.8);
    }
}

@keyframes floatDynamic {

    0%,
    100% {
        transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    }

    50% {
        transform: translateY(-40px) translateX(20px) rotate(180deg) scale(1.3);
    }
}

@keyframes floatSubtle {

    0%,
    100% {
        transform: translateY(0) translateX(0);
        opacity: 0.6;
    }

    50% {
        transform: translateY(-15px) translateX(10px);
        opacity: 1;
    }
}

@keyframes elementGlow {

    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.2);
    }
}

@keyframes cursorBlink {

    0%,
    50% {
        opacity: 1;
    }

    51%,
    100% {
        opacity: 0;
    }
}

@keyframes typeIn {
    from {
        width: 0;
    }

    to {
        width: 100%;
    }
}

@keyframes typeOut {
    from {
        width: 100%;
    }

    to {
        width: 0;
    }
}

/* ===================================
   HERO LAYOUT RESPONSIVE
   =================================== */

/* Tablet and Mobile - Stack Layout */
@media (max-width: 991px) {
    .hero-content-ultimate {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        gap: clamp(30px, 6vh, 50px);
        text-align: center;
    }

    .hero-logo-ultimate {
        grid-column: 1;
        grid-row: 1;
        height: auto;
        margin-bottom: 0;
    }

    .hero-main-content-ultimate {
        grid-column: 1;
        grid-row: 2;
        align-items: center;
        text-align: center;
    }

    .hero-title-main-ultimate {
        align-items: center;
    }

    .hero-cta-ultimate {
        justify-content: center;
    }

    .stats-container-ultimate {
        justify-content: center;
    }
}

/* Mobile Small */
@media (max-width: 575px) {
    .hero-content-ultimate {
        gap: clamp(20px, 4vh, 30px);
        padding: 0 clamp(15px, 4vw, 30px);
    }

    .hero-main-content-ultimate {
        gap: clamp(15px, 3vh, 25px);
    }
}