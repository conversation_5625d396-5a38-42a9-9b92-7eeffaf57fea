/* ===================================
   HERO RESPONSIVE ULTIMATE SYSTEM
   Perfect responsive design for all devices
   =================================== */

/* ===================================
   SCROLL INDICATOR ULTIMATE
   =================================== */

.scroll-indicator-ultimate {
    position: absolute;
    bottom: clamp(30px, 5vh, 50px);
    left: 50%;
    transform: translateX(-50%);
    z-index: 15;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    opacity: 0;
    animation: scrollIndicatorEntrance 1s ease-out forwards;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-indicator-ultimate:hover {
    transform: translateX(-50%) scale(1.1);
    opacity: 1;
}

.scroll-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.scroll-text-ultimate {
    font-size: clamp(0.8rem, 1.5vw, 0.9rem);
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.scroll-arrow-ultimate {
    position: relative;
    width: 2px;
    height: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.arrow-line {
    width: 2px;
    height: 20px;
    background: linear-gradient(to bottom,
            rgba(0, 255, 209, 0.8) 0%,
            rgba(0, 255, 209, 0.3) 100%);
    border-radius: 1px;
}

.arrow-head {
    width: 8px;
    height: 8px;
    border-right: 2px solid rgba(0, 255, 209, 0.8);
    border-bottom: 2px solid rgba(0, 255, 209, 0.8);
    transform: rotate(45deg);
    margin-top: -4px;
}

.arrow-head.arrow-up {
    transform: rotate(-135deg);
    margin-top: 4px;
}

.arrow-glow {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 100%;
    background: radial-gradient(ellipse,
            rgba(0, 255, 209, 0.3) 0%,
            transparent 70%);
    animation: arrowGlow 2s ease-in-out infinite;
}

.scroll-pulse {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: rgba(0, 255, 209, 0.8);
    border-radius: 50%;
    animation: scrollPulse 2s ease-in-out infinite;
}

/* ===================================
   RESPONSIVE BREAKPOINTS
   =================================== */

/* Ultra Wide Screens (2560px+) */
@media (min-width: 2560px) {
    .hero-content-ultimate {
        max-width: 1400px;
    }

    .energy-field-ultimate {
        width: 1000px;
        height: 1000px;
    }

    .energy-ring.ring-quaternary {
        width: 800px;
        height: 800px;
    }
}

/* Large Desktop (1920px - 2559px) */
@media (min-width: 1920px) and (max-width: 2559px) {
    .hero-content-ultimate {
        max-width: 1200px;
    }

    .energy-field-ultimate {
        width: 800px;
        height: 800px;
    }
}

/* Standard Desktop (1200px - 1919px) */
@media (min-width: 1200px) and (max-width: 1919px) {
    .hero-content-ultimate {
        max-width: 1000px;
    }

    .energy-field-ultimate {
        width: 700px;
        height: 700px;
    }

    .energy-ring.ring-quaternary {
        width: 550px;
        height: 550px;
    }

    .stats-container-ultimate {
        gap: clamp(15px, 3vw, 30px);
    }
}

/* Small Desktop (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .hero-content-ultimate {
        max-width: 900px;
        padding: 0 clamp(30px, 4vw, 60px);
    }

    .energy-field-ultimate {
        width: 600px;
        height: 600px;
    }

    .energy-ring.ring-quaternary {
        width: 480px;
        height: 480px;
    }

    .energy-ring.ring-tertiary {
        width: 380px;
        height: 380px;
    }

    .hero-main-content-ultimate {
        gap: clamp(25px, 5vh, 50px);
    }

    .cta-container-ultimate {
        gap: clamp(12px, 2.5vw, 20px);
    }
}

/* Large Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .hero-content-ultimate {
        max-width: 700px;
        padding: 0 clamp(25px, 4vw, 50px);
    }

    .energy-field-ultimate {
        width: 500px;
        height: 500px;
    }

    .energy-ring.ring-quaternary {
        width: 400px;
        height: 400px;
    }

    .energy-ring.ring-tertiary {
        width: 320px;
        height: 320px;
    }

    .energy-ring.ring-secondary {
        width: 250px;
        height: 250px;
    }

    .hero-main-content-ultimate {
        gap: clamp(20px, 4vh, 40px);
    }

    .stats-container-ultimate {
        grid-template-columns: repeat(3, 1fr);
        gap: clamp(10px, 2vw, 20px);
    }

    .stat-item-ultimate {
        padding: clamp(15px, 3vh, 25px);
    }

    .stat-visual {
        width: 50px;
        height: 50px;
    }

    .cta-container-ultimate {
        gap: 15px;
    }

    .cta-button-ultimate {
        min-width: clamp(120px, 22vw, 160px);
        padding: clamp(10px, 2vh, 15px) clamp(20px, 4vw, 30px);
    }
}

/* Small Tablet (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .hero-content-ultimate {
        max-width: 500px;
        padding: 0 clamp(20px, 4vw, 40px);
    }

    .energy-field-ultimate {
        width: 400px;
        height: 400px;
    }

    .energy-ring.ring-quaternary {
        width: 320px;
        height: 320px;
    }

    .energy-ring.ring-tertiary {
        width: 260px;
        height: 260px;
    }

    .energy-ring.ring-secondary {
        width: 200px;
        height: 200px;
    }

    .energy-ring.ring-primary {
        width: 150px;
        height: 150px;
    }

    .hero-main-content-ultimate {
        gap: clamp(18px, 4vh, 35px);
    }

    .stats-container-ultimate {
        grid-template-columns: 1fr;
        gap: 15px;
        max-width: 300px;
    }

    .stat-item-ultimate {
        padding: 15px;
        flex-direction: row;
        text-align: left;
        gap: 15px;
    }

    .stat-visual {
        width: 40px;
        height: 40px;
        flex-shrink: 0;
    }

    .stat-content {
        text-align: left;
    }

    .cta-container-ultimate {
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }

    .cta-button-ultimate {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
}

/* Mobile Large (480px - 575px) */
@media (min-width: 480px) and (max-width: 575px) {
    .hero-ultimate-professional {
        padding: clamp(20px, 4vh, 40px) 0;
    }

    .hero-content-ultimate {
        max-width: 400px;
        padding: 0 20px;
    }

    .energy-field-ultimate {
        width: 350px;
        height: 350px;
    }

    .energy-ring.ring-quaternary {
        width: 280px;
        height: 280px;
    }

    .energy-ring.ring-tertiary {
        width: 220px;
        height: 220px;
    }

    .energy-ring.ring-secondary {
        width: 170px;
        height: 170px;
    }

    .energy-ring.ring-primary {
        width: 120px;
        height: 120px;
    }

    .hero-main-content-ultimate {
        gap: clamp(15px, 3vh, 30px);
    }

    .hero-logo-ultimate {
        margin-bottom: clamp(25px, 5vh, 40px);
    }

    .title-word.word-1 .word-content {
        font-size: clamp(1.8rem, 7vw, 3rem);
    }

    .title-word.word-2 .word-content {
        font-size: clamp(2.2rem, 8vw, 3.5rem);
    }

    .title-word.word-3 .word-content {
        font-size: clamp(1rem, 3.5vw, 1.5rem);
    }

    .description-text-ultimate {
        font-size: clamp(0.9rem, 2.2vw, 1.1rem);
        line-height: 1.5;
    }
}

/* Mobile Small (320px - 479px) */
@media (max-width: 479px) {
    .hero-ultimate-professional {
        padding: clamp(15px, 3vh, 30px) 0;
    }

    .hero-content-ultimate {
        max-width: 300px;
        padding: 0 15px;
    }

    .energy-field-ultimate {
        width: 300px;
        height: 300px;
    }

    .energy-ring.ring-quaternary {
        width: 240px;
        height: 240px;
    }

    .energy-ring.ring-tertiary {
        width: 190px;
        height: 190px;
    }

    .energy-ring.ring-secondary {
        width: 140px;
        height: 140px;
    }

    .energy-ring.ring-primary {
        width: 100px;
        height: 100px;
    }

    .hero-main-content-ultimate {
        gap: clamp(12px, 3vh, 25px);
    }

    .hero-logo-ultimate {
        margin-bottom: clamp(20px, 4vh, 35px);
    }

    .title-word.word-1 .word-content {
        font-size: clamp(1.5rem, 6vw, 2.5rem);
    }

    .title-word.word-2 .word-content {
        font-size: clamp(1.8rem, 7vw, 3rem);
    }

    .title-word.word-3 .word-content {
        font-size: clamp(0.9rem, 3vw, 1.3rem);
    }

    .description-text-ultimate {
        font-size: clamp(0.85rem, 2vw, 1rem);
        line-height: 1.4;
    }

    .stats-container-ultimate {
        grid-template-columns: 1fr;
        gap: 10px;
        max-width: 250px;
    }

    .stat-item-ultimate {
        padding: 12px;
        flex-direction: row;
        gap: 12px;
    }

    .stat-visual {
        width: 35px;
        height: 35px;
    }

    .cta-button-ultimate {
        width: 100%;
        max-width: 250px;
        padding: 12px 20px;
        font-size: clamp(0.85rem, 1.8vw, 1rem);
    }

    .scroll-indicator-ultimate {
        bottom: clamp(20px, 4vh, 30px);
    }

    .scroll-text-ultimate {
        font-size: 0.75rem;
    }

    .arrow-line {
        height: 15px;
    }

    .arrow-head {
        width: 6px;
        height: 6px;
    }
}

/* ===================================
   RESPONSIVE ANIMATIONS
   =================================== */

@keyframes scrollIndicatorEntrance {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }

    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes arrowGlow {

    0%,
    100% {
        opacity: 0.3;
        transform: translateX(-50%) scaleY(1);
    }

    50% {
        opacity: 0.8;
        transform: translateX(-50%) scaleY(1.2);
    }
}

@keyframes scrollPulse {
    0% {
        opacity: 1;
        transform: translateX(-50%) translateY(0) scale(1);
    }

    50% {
        opacity: 0.5;
        transform: translateX(-50%) translateY(15px) scale(0.8);
    }

    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(30px) scale(1);
    }
}

/* ===================================
   PERFORMANCE OPTIMIZATIONS
   =================================== */

/* Reduce animations on low-end devices */
@media (prefers-reduced-motion: reduce) {

    .energy-ring,
    .pulse,
    .particle-layer,
    .wave-layer,
    .floating-element-ultimate,
    .logo-ring,
    .scroll-pulse {
        animation: none !important;
    }

    .hero-title-ultimate[data-animate="title-cascade"] .title-word,
    .hero-description-ultimate[data-animate="description-fade-in"],
    .hero-stats-ultimate[data-animate="stats-counter"] .stat-item-ultimate,
    .hero-cta-ultimate[data-animate="cta-entrance"],
    .scroll-indicator-ultimate {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
    }
}

/* GPU acceleration for smooth animations */
.energy-ring,
.pulse,
.particle-layer,
.wave-layer,
.floating-element-ultimate,
.logo-ring,
.hero-logo-ultimate-main,
.title-word,
.stat-item-ultimate,
.cta-button-ultimate {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* ===================================
   DARK MODE SUPPORT
   =================================== */

@media (prefers-color-scheme: dark) {
    .hero-ultimate-professional {
        background: #000000;
    }

    .gradient-primary {
        background: radial-gradient(ellipse at center,
                rgba(0, 163, 255, 0.2) 0%,
                rgba(0, 255, 209, 0.1) 35%,
                rgba(0, 0, 0, 0.98) 70%,
                rgba(0, 0, 0, 1) 100%);
    }
}

/* ===================================
   HIGH CONTRAST MODE
   =================================== */

@media (prefers-contrast: high) {
    .title-word.word-1 .word-content {
        color: #ffffff;
        text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
    }

    .title-word.word-2 .word-content {
        background: linear-gradient(135deg, #00d4ff 0%, #00ffaa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .description-text-ultimate {
        color: rgba(255, 255, 255, 0.95);
    }

    .energy-ring {
        border-width: 2px;
        opacity: 0.8;
    }
}

/* ===================================
   PRINT STYLES
   =================================== */

/* ===================================
   SMOOTH SCROLLING STYLES
   =================================== */

.smooth-scrolling {
    overflow: hidden;
}

.smooth-scrolling * {
    pointer-events: none;
}

/* Back to top indicator styling */
.scroll-indicator-ultimate.back-to-top .scroll-text-ultimate {
    color: rgba(255, 255, 255, 0.9);
}

.scroll-indicator-ultimate.back-to-top .arrow-line {
    background: linear-gradient(to top,
            rgba(0, 255, 209, 0.8) 0%,
            rgba(0, 255, 209, 0.3) 100%);
}

.scroll-indicator-ultimate.back-to-top:hover {
    transform: translateX(-50%) scale(1.1) translateY(-5px);
}

/* Next section indicator styling */
.scroll-indicator-ultimate.next-section:hover {
    transform: translateX(-50%) scale(1.1) translateY(5px);
}

/* Section entrance animations */
.animate-in {
    animation-play-state: running !important;
}

/* ===================================
   PRINT STYLES
   =================================== */

@media print {
    .hero-ultimate-professional {
        background: white !important;
        color: black !important;
        height: auto !important;
        overflow: visible !important;
    }

    .hero-background-ultimate,
    .energy-field-ultimate,
    .particle-system-ultimate,
    .geometric-art-system,
    .wave-system-ultimate,
    .hero-floating-ultimate,
    .scroll-indicator-ultimate {
        display: none !important;
    }

    .hero-content-ultimate {
        position: static !important;
        max-width: none !important;
        padding: 20px !important;
        grid-template-columns: 1fr !important;
    }

    .title-word .word-content {
        color: black !important;
        background: none !important;
        -webkit-text-fill-color: black !important;
        text-shadow: none !important;
    }

    .description-text-ultimate {
        color: black !important;
    }

    .cta-button-ultimate {
        border: 2px solid black !important;
        background: white !important;
        color: black !important;
    }
}