function initEnergyParticles() {
    const canvas = document.getElementById('energyParticles');

    // Check if canvas exists before proceeding
    if (!canvas) {
        console.warn('Energy particles canvas not found');
        return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
        console.warn('Canvas context not available');
        return;
    }

    // تنظیم اندازه canvas
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // تعریف کلاس ذره انرژی
    class EnergyParticle {
        constructor() {
            this.reset();
        }

        reset() {
            this.x = Math.random() * canvas.width;
            this.y = Math.random() * canvas.height;
            this.size = Math.random() * 3 + 1;
            this.speedX = Math.random() * 3 - 1.5;
            this.speedY = Math.random() * 3 - 1.5;
            this.energy = Math.random();
            this.color = `hsla(${190 + Math.random() * 40}, 100%, 60%, ${this.energy})`;
        }

        update() {
            this.x += this.speedX;
            this.y += this.speedY;
            this.energy -= 0.01;

            if (this.energy <= 0 ||
                this.x < 0 || this.x > canvas.width ||
                this.y < 0 || this.y > canvas.height) {
                this.reset();
            }
        }

        draw() {
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fillStyle = this.color;
            ctx.fill();

            // افکت درخشش
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size * 2, 0, Math.PI * 2);
            ctx.fillStyle = this.color.replace('1)', '0.2)');
            ctx.fill();
        }
    }

    // ایجاد آرایه‌ای از ذرات
    const particles = Array.from({ length: 100 }, () => new EnergyParticle());

    // تابع انیمیشن
    function animate() {
        ctx.fillStyle = 'rgba(0, 31, 63, 0.1)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        particles.forEach(particle => {
            particle.update();
            particle.draw();
        });

        // ایجاد خطوط اتصال بین ذرات نزدیک
        particles.forEach((p1, i) => {
            particles.slice(i + 1).forEach(p2 => {
                const dx = p1.x - p2.x;
                const dy = p1.y - p2.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 100) {
                    ctx.beginPath();
                    ctx.strokeStyle = `rgba(0, 163, 255, ${0.2 * (1 - distance / 100)})`;
                    ctx.lineWidth = 0.5;
                    ctx.moveTo(p1.x, p1.y);
                    ctx.lineTo(p2.x, p2.y);
                    ctx.stroke();
                }
            });
        });

        requestAnimationFrame(animate);
    }

    animate();
}

// اجرای انیمیشن‌ها
document.addEventListener('DOMContentLoaded', () => {
    const ctaButton = document.querySelector('.cta-button');
    if (!ctaButton) {
        console.warn('CTA button not found');
        return;
    }

    initEnergyParticles();

    // Initialize Splitting
    if (typeof Splitting !== 'undefined') {
        Splitting({ by: 'chars' });
    }

    // Title Animation
    const words = document.querySelectorAll('.animated-word');
    anime.timeline({
        easing: 'easeOutExpo'
    })
        .add({
            targets: '.animated-word',
            translateY: [50, 0],
            opacity: [0, 1],
            duration: 1200,
            delay: anime.stagger(100),
            begin: (anim) => {
                document.querySelectorAll('.animated-word').forEach(word => {
                    word.style.opacity = '0';
                    word.style.transform = 'translateY(50px)';
                });
            }
        });

    // Description Card Animation
    const descriptionCard = document.querySelector('.hero-description-wrapper') ||
        document.querySelector('.hero-description') ||
        document.querySelector('.content-wrapper');
    if (!descriptionCard) {
        // Silently skip if description card not found
        return;
    }

    // Mouse move animation
    descriptionCard.addEventListener('mousemove', (e) => {
        const rect = descriptionCard.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const xPercent = (x / rect.width - 0.5) * 20;
        const yPercent = (y / rect.height - 0.5) * 20;

        anime({
            targets: descriptionCard,
            rotateX: -yPercent,
            rotateY: xPercent,
            duration: 400,
            easing: 'easeOutExpo'
        });
    });

    // Mouse leave animation
    descriptionCard.addEventListener('mouseleave', () => {
        anime({
            targets: descriptionCard,
            rotateX: 0,
            rotateY: 0,
            duration: 600,
            easing: 'easeOutExpo'
        });
    });

    // Stats Animation
    const stats = document.querySelectorAll('.stat-item');
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px'
    };

    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const stat = entry.target;
                const statNumber = stat.querySelector('.stat-number');
                if (!statNumber) return;

                // Get value from data-value attribute or parse from text content
                let value = parseInt(statNumber.dataset.value, 10);

                // If no data-value attribute, try to parse from text content
                if (isNaN(value)) {
                    const textContent = statNumber.textContent.trim();
                    const numberMatch = textContent.match(/\d+/);
                    if (numberMatch) {
                        value = parseInt(numberMatch[0], 10);
                    } else {
                        value = 0;
                    }
                }

                if (isNaN(value) || value <= 0) return;

                anime({
                    targets: stat,
                    translateY: [30, 0],
                    opacity: [0, 1],
                    duration: 800,
                    easing: 'easeOutExpo'
                });

                // Safe number animation with suffix preservation
                const originalText = statNumber.textContent.trim();
                const suffix = originalText.replace(/\d+/, '').trim();

                let currentValue = 0;
                const increment = value / 100;
                const animateNumber = () => {
                    if (currentValue < value) {
                        currentValue += increment;
                        statNumber.textContent = Math.floor(currentValue) + suffix;
                        requestAnimationFrame(animateNumber);
                    } else {
                        statNumber.textContent = value + suffix;
                    }
                };
                animateNumber();

                statsObserver.unobserve(stat);
            }
        });
    }, observerOptions);

    stats.forEach(stat => statsObserver.observe(stat));

    // Button Hover Effects
    document.querySelectorAll('.cta-button').forEach(button => {
        button.addEventListener('mouseenter', () => {
            anime({
                targets: button.querySelector('.button-particles'),
                scale: [1, 1.5],
                opacity: [0, 0.3],
                duration: 800,
                easing: 'easeOutExpo'
            });
        });

        button.addEventListener('mouseleave', () => {
            anime({
                targets: button.querySelector('.button-particles'),
                scale: [1.5, 1],
                opacity: [0.3, 0],
                duration: 400,
                easing: 'easeOutExpo'
            });
        });
    });
});





