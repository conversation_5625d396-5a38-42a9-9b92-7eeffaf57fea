document.addEventListener('DOMContentLoaded', () => {
    const header = document.querySelector('.site-header');
    const heroSection = document.querySelector('.hero-section-professional');

    if (!header) {
        console.warn('Header not found');
        return;
    }

    // Scroll Handler - Improved
    let lastScroll = 0;
    const scrollThreshold = 50;
    const heroHeight = heroSection ? heroSection.offsetHeight : window.innerHeight;

    function updateHeaderState() {
        const currentScroll = window.pageYOffset;

        // Add/remove classes based on scroll position
        if (currentScroll < heroHeight * 0.8) {
            header.classList.add('hero-overlay');
            header.classList.remove('scrolled');
        } else {
            header.classList.remove('hero-overlay');
            header.classList.add('scrolled');
        }

        // Smooth hide/show on scroll (optional)
        if (currentScroll > lastScroll && currentScroll > scrollThreshold) {
            // Scrolling down - keep header visible for navigation
            header.style.transform = 'translateY(0)';
        } else {
            // Scrolling up
            header.style.transform = 'translateY(0)';
        }

        lastScroll = currentScroll;
    }

    // Initial state
    updateHeaderState();

    // Throttled scroll listener
    let ticking = false;
    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(() => {
                updateHeaderState();
                ticking = false;
            });
            ticking = true;
        }
    });

    // Particle Animation for CTA Button
    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'button-particle';

        const size = Math.random() * 3 + 1;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        const startX = Math.random() * 100;
        const startY = Math.random() * 100;
        particle.style.left = `${startX}%`;
        particle.style.top = `${startY}%`;

        return particle;
    }

    function initParticles() {
        const ctaButton = document.querySelector('.cta-button');
        if (!ctaButton) {
            console.warn('CTA button not found');
            return;
        }

        const particlesContainer = ctaButton.querySelector('.btn-particles');
        if (!particlesContainer) {
            // Create particles container if it doesn't exist
            const newParticlesContainer = document.createElement('div');
            newParticlesContainer.className = 'btn-particles';
            ctaButton.appendChild(newParticlesContainer);
            return;
        }

        for (let i = 0; i < 15; i++) {
            particlesContainer.appendChild(createParticle());
        }
    }

    initParticles();

    // Active Link Indicator
    const navLinks = document.querySelectorAll('.nav-link');

    function updateActiveLink() {
        const scrollPosition = window.scrollY;

        navLinks.forEach(link => {
            const sectionId = link.getAttribute('href');
            const section = document.querySelector(sectionId);

            if (section) {
                const sectionTop = section.offsetTop - 100;
                const sectionBottom = sectionTop + section.offsetHeight;

                if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            }
        });
    }

    window.addEventListener('scroll', updateActiveLink);
    updateActiveLink();
});


