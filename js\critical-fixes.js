/**
 * Critical Fixes for INOVA Energy Theme
 * Fixes header overlay, blue page issue, and animation conflicts
 */

(function() {
    'use strict';

    // Prevent blue page issue
    function preventBluePageIssue() {
        // Ensure body has proper background
        document.body.style.background = 'var(--secondary)';
        document.body.style.color = 'var(--text-light)';
        
        // Remove any problematic classes that might cause blue background
        const problematicClasses = ['blue-bg', 'blue-background', 'full-blue'];
        problematicClasses.forEach(className => {
            document.body.classList.remove(className);
        });
    }

    // Fix header overlay
    function initHeaderOverlay() {
        const header = document.querySelector('.site-header');
        const heroSection = document.querySelector('.hero-section-professional');
        
        if (!header) {
            console.warn('Header not found');
            return;
        }

        // Initial state - set hero overlay
        header.classList.add('hero-overlay');
        
        // Get hero height
        const heroHeight = heroSection ? heroSection.offsetHeight : window.innerHeight;
        
        // Throttled scroll handler
        let ticking = false;
        function updateHeaderState() {
            const scrollY = window.pageYOffset;
            const heroThreshold = heroHeight * 0.8;
            
            if (scrollY < heroThreshold) {
                header.classList.add('hero-overlay');
                header.classList.remove('scrolled');
            } else {
                header.classList.remove('hero-overlay');
                header.classList.add('scrolled');
            }
        }
        
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateHeaderState();
                    ticking = false;
                });
                ticking = true;
            }
        }
        
        // Initial call
        updateHeaderState();
        
        // Add scroll listener
        window.addEventListener('scroll', onScroll, { passive: true });
        
        // Update on resize
        window.addEventListener('resize', () => {
            setTimeout(updateHeaderState, 100);
        }, { passive: true });
    }

    // Fix canvas errors
    function fixCanvasErrors() {
        // Check for canvas elements and ensure they exist before scripts try to use them
        const canvasIds = ['energyParticles', 'hero-particles', 'background-canvas'];
        
        canvasIds.forEach(id => {
            if (!document.getElementById(id)) {
                // Create canvas if it doesn't exist but is needed
                const canvas = document.createElement('canvas');
                canvas.id = id;
                canvas.style.position = 'absolute';
                canvas.style.top = '0';
                canvas.style.left = '0';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.pointerEvents = 'none';
                canvas.style.zIndex = '1';
                
                // Append to hero section if it exists
                const heroSection = document.querySelector('.hero-section-professional');
                if (heroSection) {
                    heroSection.appendChild(canvas);
                }
            }
        });
    }

    // Fix animation conflicts
    function fixAnimationConflicts() {
        // Prevent conflicting animations
        const style = document.createElement('style');
        style.textContent = `
            /* Prevent animation conflicts */
            * {
                animation-fill-mode: both !important;
            }
            
            /* Ensure smooth transitions */
            body.transitioning * {
                transition: all 0.3s ease !important;
            }
            
            /* Fix any blue background issues */
            body {
                background: var(--secondary) !important;
            }
            
            /* Ensure proper stacking */
            .site-header {
                z-index: 1000 !important;
            }
            
            .hero-container-professional {
                z-index: 10 !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Fix stats animation
    function fixStatsAnimation() {
        // Ensure stats elements have proper data attributes
        const statElements = document.querySelectorAll('.stat-number, .stat-value');
        statElements.forEach(stat => {
            const text = stat.textContent.trim();
            const numberMatch = text.match(/(\d+)/);
            if (numberMatch && !stat.getAttribute('data-value')) {
                stat.setAttribute('data-value', numberMatch[1]);
            }
        });
    }

    // Initialize all fixes
    function initCriticalFixes() {
        // Run immediately
        preventBluePageIssue();
        fixAnimationConflicts();
        fixCanvasErrors();
        
        // Run after DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                initHeaderOverlay();
                fixStatsAnimation();
            });
        } else {
            initHeaderOverlay();
            fixStatsAnimation();
        }
        
        // Run after page load
        window.addEventListener('load', () => {
            preventBluePageIssue();
            fixStatsAnimation();
        });
        
        // Fix on language change
        document.addEventListener('languageChanged', () => {
            setTimeout(() => {
                preventBluePageIssue();
                fixStatsAnimation();
            }, 100);
        });
    }

    // Start fixes
    initCriticalFixes();

    // Expose functions globally for debugging
    window.inovaCriticalFixes = {
        preventBluePageIssue,
        initHeaderOverlay,
        fixCanvasErrors,
        fixAnimationConflicts,
        fixStatsAnimation
    };

})();
