/**
 * INOVA Energy - Typing Animation System
 * Beautiful typing animation for hero subtitle
 */

class TypingAnimation {
    constructor() {
        this.typingElement = null;
        this.cursorElement = null;
        this.phrases = [
            'پیشگام انرژی پایدار',
            'نوآوری در صنعت انرژی',
            'راهکارهای هوشمند انرژی',
            'آینده‌ای سبز و پایدار'
        ];
        this.currentPhraseIndex = 0;
        this.currentCharIndex = 0;
        this.isDeleting = false;
        this.isWaiting = false;
        this.typeSpeed = 100;
        this.deleteSpeed = 50;
        this.waitTime = 2000;
        this.isAnimating = false;
        
        this.init();
    }

    init() {
        this.setupElements();
        this.setupObserver();
        console.log('Typing Animation initialized');
    }

    /**
     * Setup DOM elements
     */
    setupElements() {
        this.typingElement = document.getElementById('typingText');
        this.cursorElement = document.querySelector('.typing-cursor');
        
        if (!this.typingElement) {
            console.warn('Typing element not found');
            return;
        }

        // Set initial text
        this.typingElement.textContent = '';
        console.log('Typing elements found and initialized');
    }

    /**
     * Setup intersection observer to start animation when visible
     */
    setupObserver() {
        if (!this.typingElement) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.isAnimating) {
                    this.startTyping();
                    this.isAnimating = true;
                    observer.unobserve(entry.target);
                }
            });
        }, { 
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        });

        observer.observe(this.typingElement.closest('.hero-title-ultimate'));
    }

    /**
     * Start typing animation
     */
    startTyping() {
        if (!this.typingElement) return;
        
        // Start with first phrase
        this.currentPhraseIndex = 0;
        this.currentCharIndex = 0;
        this.isDeleting = false;
        this.isWaiting = false;
        
        this.typeText();
    }

    /**
     * Main typing function
     */
    typeText() {
        if (!this.typingElement) return;

        const currentPhrase = this.phrases[this.currentPhraseIndex];
        
        if (this.isWaiting) {
            // Wait before starting to delete
            setTimeout(() => {
                this.isWaiting = false;
                this.isDeleting = true;
                this.typeText();
            }, this.waitTime);
            return;
        }

        if (this.isDeleting) {
            // Delete characters
            if (this.currentCharIndex > 0) {
                this.currentCharIndex--;
                this.typingElement.textContent = currentPhrase.substring(0, this.currentCharIndex);
                
                setTimeout(() => this.typeText(), this.deleteSpeed);
            } else {
                // Move to next phrase
                this.isDeleting = false;
                this.currentPhraseIndex = (this.currentPhraseIndex + 1) % this.phrases.length;
                
                setTimeout(() => this.typeText(), 200);
            }
        } else {
            // Type characters
            if (this.currentCharIndex < currentPhrase.length) {
                this.currentCharIndex++;
                this.typingElement.textContent = currentPhrase.substring(0, this.currentCharIndex);
                
                setTimeout(() => this.typeText(), this.typeSpeed);
            } else {
                // Finished typing, wait before deleting
                this.isWaiting = true;
                this.typeText();
            }
        }
    }

    /**
     * Pause animation
     */
    pause() {
        this.isAnimating = false;
    }

    /**
     * Resume animation
     */
    resume() {
        if (!this.isAnimating) {
            this.isAnimating = true;
            this.typeText();
        }
    }

    /**
     * Reset animation
     */
    reset() {
        this.currentPhraseIndex = 0;
        this.currentCharIndex = 0;
        this.isDeleting = false;
        this.isWaiting = false;
        this.isAnimating = false;
        
        if (this.typingElement) {
            this.typingElement.textContent = '';
        }
    }

    /**
     * Update phrases
     */
    updatePhrases(newPhrases) {
        this.phrases = newPhrases;
        this.reset();
    }

    /**
     * Update typing speed
     */
    updateSpeed(typeSpeed, deleteSpeed = null) {
        this.typeSpeed = typeSpeed;
        if (deleteSpeed !== null) {
            this.deleteSpeed = deleteSpeed;
        }
    }

    /**
     * Destroy animation
     */
    destroy() {
        this.reset();
        console.log('Typing Animation destroyed');
    }
}

// CSS for typing animation
const typingAnimationCSS = `
.typing-container {
    position: relative;
    display: inline-block;
}

.typing-text {
    display: inline-block;
    min-height: 1em;
}

.typing-cursor {
    display: inline-block;
    margin-left: 2px;
    color: #00ffd1;
    animation: typingCursorBlink 1s infinite;
    font-weight: 300;
}

@keyframes typingCursorBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Smooth text transitions */
.typing-text {
    transition: none;
    font-family: inherit;
    font-weight: inherit;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    background: inherit;
    -webkit-background-clip: inherit;
    -webkit-text-fill-color: inherit;
    background-clip: inherit;
}

/* Ensure proper RTL support for Persian text */
.typing-container {
    direction: rtl;
    text-align: inherit;
}

.typing-text {
    direction: rtl;
    unicode-bidi: embed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .typing-cursor {
        margin-left: 1px;
    }
}

@media (max-width: 480px) {
    .typing-cursor {
        font-size: 0.9em;
    }
}
`;

// Inject CSS
const styleSheet = document.createElement('style');
styleSheet.textContent = typingAnimationCSS;
document.head.appendChild(styleSheet);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new TypingAnimation();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TypingAnimation;
}
