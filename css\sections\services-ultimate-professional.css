/* ===================================
   SERVICES SECTION - ULTIMATE PROFESSIONAL DESIGN
   Matching Hero Section Style
   =================================== */

/* ===================================
   SERVICES SECTION BASE
   =================================== */

.services-section-ultimate {
    position: relative;
    width: 100vw;
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0a0a0a;
    z-index: 1;
}

/* ===================================
   SERVICES BACKGROUND SYSTEM
   =================================== */

.services-background-ultimate {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

/* Base Gradient Layers */
.services-bg-gradient-base {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.services-gradient-primary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse 700px 500px at 20% 30%,
            rgba(0, 163, 255, 0.12) 0%,
            transparent 60%),
        radial-gradient(ellipse 500px 700px at 80% 70%,
            rgba(0, 255, 209, 0.1) 0%,
            transparent 60%),
        radial-gradient(ellipse 900px 300px at 50% 20%,
            rgba(138, 43, 226, 0.06) 0%,
            transparent 70%),
        linear-gradient(225deg,
            rgba(0, 0, 0, 0.9) 0%,
            rgba(10, 10, 10, 0.95) 50%,
            rgba(0, 0, 0, 1) 100%);
    animation: servicesGradientPulse 18s ease-in-out infinite;
}

.services-gradient-secondary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(from 45deg at 50% 50%,
            rgba(0, 255, 209, 0.08) 0deg,
            rgba(0, 163, 255, 0.04) 120deg,
            rgba(138, 43, 226, 0.06) 240deg,
            rgba(0, 255, 209, 0.08) 360deg);
    animation: servicesGradientRotate 25s linear infinite;
}

/* ===================================
   SERVICES DECORATIVE ELEMENTS
   =================================== */

.services-decorative-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.services-logo-watermark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    background-image: url('../../../Logo1.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.05;
    animation: logoWatermark 20s ease-in-out infinite;
}

.services-floating-icons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.services-icon-element {
    position: absolute;
    width: 40px;
    height: 40px;
    background: rgba(0, 255, 209, 0.1);
    border: 1px solid rgba(0, 255, 209, 0.3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: iconFloat 15s ease-in-out infinite;
}

.services-icon-element.icon-1 {
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.services-icon-element.icon-2 {
    top: 25%;
    right: 15%;
    animation-delay: 3s;
}

.services-icon-element.icon-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 6s;
}

.services-icon-element.icon-4 {
    bottom: 20%;
    right: 10%;
    animation-delay: 9s;
}

.services-icon-element::before {
    content: '';
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #00a3ff, #00ffd1);
    border-radius: 4px;
    opacity: 0.7;
}

/* ===================================
   SERVICES CONTENT CONTAINER
   =================================== */

.services-content-ultimate {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 clamp(20px, 5vw, 80px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

/* ===================================
   SERVICES HEADER
   =================================== */

.services-header-ultimate {
    text-align: center;
    margin-bottom: clamp(40px, 8vh, 80px);
}

.services-title-ultimate {
    margin: 0 0 clamp(20px, 4vh, 30px) 0;
    line-height: 1.1;
}

.services-title-word {
    display: block;
    margin-bottom: clamp(5px, 1vh, 10px);
}

.services-title-word.word-1 {
    font-size: clamp(1.8rem, 6vw, 3rem);
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.services-title-word.word-2 {
    font-size: clamp(2.2rem, 7vw, 3.5rem);
    font-weight: 800;
    background: linear-gradient(135deg, #00a3ff 0%, #00ffd1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.services-description-ultimate {
    font-size: clamp(1rem, 2.2vw, 1.2rem);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto;
}

/* ===================================
   SERVICES GRID
   =================================== */

.services-grid-ultimate {
    display: grid;
    gap: clamp(20px, 3vh, 30px);
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card-ultimate {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: clamp(25px, 4vh, 35px);
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    overflow: hidden;
}

.service-card-ultimate::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.05) 0%,
            transparent 50%,
            rgba(0, 163, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.service-card-ultimate:hover::before {
    opacity: 1;
}

.service-card-ultimate:hover {
    transform: translateY(-5px);
    border-color: rgba(0, 255, 209, 0.3);
    box-shadow: 0 15px 40px rgba(0, 255, 209, 0.15);
}

.service-content-ultimate {
    position: relative;
    z-index: 2;
}

.service-icon-ultimate {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00a3ff, #00ffd1);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: clamp(15px, 3vh, 20px);
    box-shadow: 0 8px 25px rgba(0, 255, 209, 0.3);
}

.service-title-card {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    font-weight: 600;
    color: #ffffff;
    margin-bottom: clamp(10px, 2vh, 15px);
    line-height: 1.3;
}

.service-description-card {
    font-size: clamp(0.9rem, 2vw, 1rem);
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: clamp(15px, 3vh, 20px);
}

.service-features-ultimate {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-feature-item {
    font-size: clamp(0.85rem, 1.8vw, 0.95rem);
    color: rgba(255, 255, 255, 0.6);
    padding: clamp(3px, 0.5vh, 5px) 0;
    position: relative;
    padding-left: 20px;
}

.service-feature-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: #00ffd1;
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(0, 255, 209, 0.6);
}

/* ===================================
   SERVICES ANIMATIONS
   =================================== */

@keyframes servicesGradientPulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.02) rotate(1deg);
    }
}

@keyframes servicesGradientRotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes logoWatermark {

    0%,
    100% {
        opacity: 0.05;
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
    }

    50% {
        opacity: 0.08;
        transform: translate(-50%, -50%) scale(1.1) rotate(5deg);
    }
}

@keyframes iconFloat {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.6;
    }

    25% {
        transform: translateY(-15px) rotate(5deg);
        opacity: 0.8;
    }

    50% {
        transform: translateY(-8px) rotate(-3deg);
        opacity: 1;
    }

    75% {
        transform: translateY(-20px) rotate(8deg);
        opacity: 0.7;
    }
}

/* ===================================
   SERVICES RESPONSIVE DESIGN
   =================================== */

/* Desktop Large (1920px+) */
@media (min-width: 1920px) {
    .services-grid-ultimate {
        grid-template-columns: repeat(4, 1fr);
        max-width: 1400px;
    }
}

/* Desktop Standard (1200px - 1919px) */
@media (min-width: 1200px) and (max-width: 1919px) {
    .services-grid-ultimate {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1200px;
    }
}

/* Desktop Small (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .services-grid-ultimate {
        grid-template-columns: repeat(2, 1fr);
        max-width: 900px;
    }

    .services-logo-watermark {
        width: 250px;
        height: 250px;
    }
}

/* Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .services-grid-ultimate {
        grid-template-columns: repeat(2, 1fr);
        max-width: 700px;
    }

    .services-logo-watermark {
        width: 200px;
        height: 200px;
    }

    .service-card-ultimate {
        padding: clamp(20px, 3vh, 25px);
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .services-grid-ultimate {
        grid-template-columns: 1fr;
        max-width: 400px;
    }

    .services-logo-watermark {
        width: 150px;
        height: 150px;
        opacity: 0.03;
    }

    .services-icon-element {
        width: 30px;
        height: 30px;
    }

    .services-icon-element::before {
        width: 15px;
        height: 15px;
    }

    .service-icon-ultimate {
        width: 50px;
        height: 50px;
    }
}