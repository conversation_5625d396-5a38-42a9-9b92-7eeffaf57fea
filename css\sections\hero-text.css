/* Hero Description Card Styles - Contained */
.hero-description-wrapper {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: clamp(1rem, 2vw, 2rem);
    transition: transform 0.3s ease;
    transform-style: preserve-3d;
    perspective: 1000px;
    box-shadow:
        0 4px 6px rgba(0, 0, 0, 0.1),
        0 1px 3px rgba(0, 0, 0, 0.08);
    max-height: min(600px, 70vh);
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

/* Neo Glass Effect */
.neo-glass {
    background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1),
            rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 8px 32px 0 rgba(0, 0, 0, 0.36),
        inset 0 0 0 1px rgba(255, 255, 255, 0.08);
}

/* Content Layout */
.content-body {
    position: relative;
    z-index: 1;
}

.compact-layout {
    max-width: 800px;
    margin: 0 auto;
}

/* Section Title */
.section-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    position: relative;
}

/* Gradient Text Effect */
.gradient-text {
    background: linear-gradient(45deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

/* Description Text */
.main-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
}

.secondary-description {
    font-size: 1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.7);
}

/* Hover Effects */
.hero-description-wrapper:hover {
    transform: translateZ(20px);
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-description-wrapper {
        padding: 1.5rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .main-description {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .hero-description-wrapper {
        padding: 1.25rem;
        margin: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .main-description,
    .secondary-description {
        font-size: 0.9rem;
    }
}

/* CSS Variables */
:root {
    --primary: #00a3ff;
    --accent: #00ffd1;
    --text-primary: rgba(255, 255, 255, 0.9);
    --text-secondary: rgba(255, 255, 255, 0.7);
}