/**
 * INOVA Energy - Hero Typing Animation
 * Beautiful typing effect for rotating taglines
 */

class HeroTypingAnimation {
    constructor() {
        this.typingElement = null;
        this.cursorElement = null;
        this.phrases = [
            'پیشگام انرژی پایدار',
            'نوآوری در صنعت انرژی',
            'راهکارهای هوشمند انرژی',
            'آینده‌ای سبز و پایدار'
        ];
        this.currentPhraseIndex = 0;
        this.currentCharIndex = 0;
        this.isTyping = true;
        this.isDeleting = false;
        this.typingSpeed = 100;
        this.deletingSpeed = 50;
        this.pauseTime = 2000;
        this.isInitialized = false;
        
        this.init();
    }

    init() {
        this.setupElements();
        if (this.typingElement) {
            this.startTypingAnimation();
            console.log('Hero Typing Animation initialized');
        }
    }

    /**
     * Setup DOM elements
     */
    setupElements() {
        this.typingElement = document.getElementById('typingText');
        this.cursorElement = document.querySelector('.typing-cursor');
        
        if (!this.typingElement) {
            console.warn('Typing element not found');
            return;
        }

        // Clear initial text
        this.typingElement.textContent = '';
    }

    /**
     * Start the typing animation cycle
     */
    startTypingAnimation() {
        if (!this.typingElement) return;

        // Wait a bit before starting
        setTimeout(() => {
            this.typePhrase();
        }, 1000);
    }

    /**
     * Type current phrase character by character
     */
    typePhrase() {
        const currentPhrase = this.phrases[this.currentPhraseIndex];
        
        if (this.isDeleting) {
            // Deleting characters
            if (this.currentCharIndex > 0) {
                this.currentCharIndex--;
                this.typingElement.textContent = currentPhrase.substring(0, this.currentCharIndex);
                
                setTimeout(() => this.typePhrase(), this.deletingSpeed);
            } else {
                // Finished deleting, move to next phrase
                this.isDeleting = false;
                this.currentPhraseIndex = (this.currentPhraseIndex + 1) % this.phrases.length;
                
                setTimeout(() => this.typePhrase(), 200);
            }
        } else {
            // Typing characters
            if (this.currentCharIndex < currentPhrase.length) {
                this.currentCharIndex++;
                this.typingElement.textContent = currentPhrase.substring(0, this.currentCharIndex);
                
                setTimeout(() => this.typePhrase(), this.typingSpeed);
            } else {
                // Finished typing, pause then start deleting
                setTimeout(() => {
                    this.isDeleting = true;
                    this.typePhrase();
                }, this.pauseTime);
            }
        }
    }

    /**
     * Pause animation
     */
    pause() {
        this.isTyping = false;
    }

    /**
     * Resume animation
     */
    resume() {
        if (!this.isTyping) {
            this.isTyping = true;
            this.typePhrase();
        }
    }

    /**
     * Change typing speed
     */
    setSpeed(typingSpeed, deletingSpeed) {
        this.typingSpeed = typingSpeed || this.typingSpeed;
        this.deletingSpeed = deletingSpeed || this.deletingSpeed;
    }

    /**
     * Add new phrase to rotation
     */
    addPhrase(phrase) {
        if (phrase && !this.phrases.includes(phrase)) {
            this.phrases.push(phrase);
        }
    }

    /**
     * Remove phrase from rotation
     */
    removePhrase(phrase) {
        const index = this.phrases.indexOf(phrase);
        if (index > -1 && this.phrases.length > 1) {
            this.phrases.splice(index, 1);
            
            // Adjust current index if needed
            if (this.currentPhraseIndex >= this.phrases.length) {
                this.currentPhraseIndex = 0;
            }
        }
    }

    /**
     * Set new phrases array
     */
    setPhrases(newPhrases) {
        if (Array.isArray(newPhrases) && newPhrases.length > 0) {
            this.phrases = [...newPhrases];
            this.currentPhraseIndex = 0;
            this.currentCharIndex = 0;
            this.isDeleting = false;
        }
    }

    /**
     * Get current phrase
     */
    getCurrentPhrase() {
        return this.phrases[this.currentPhraseIndex];
    }

    /**
     * Reset animation to first phrase
     */
    reset() {
        this.currentPhraseIndex = 0;
        this.currentCharIndex = 0;
        this.isDeleting = false;
        
        if (this.typingElement) {
            this.typingElement.textContent = '';
        }
    }

    /**
     * Destroy animation
     */
    destroy() {
        this.pause();
        
        if (this.typingElement) {
            this.typingElement.textContent = this.phrases[0];
        }
        
        console.log('Hero Typing Animation destroyed');
    }
}

// CSS for typing animation effects
const typingAnimationCSS = `
.typing-text {
    display: inline-block;
    min-width: 1ch;
    position: relative;
}

.typing-container {
    overflow: hidden;
    white-space: nowrap;
}

.typing-cursor {
    display: inline-block;
    font-weight: 100;
    font-size: 1em;
    vertical-align: baseline;
}

/* Smooth text transitions */
.typing-text {
    transition: none;
}

/* RTL support for Persian text */
.typing-container {
    direction: rtl;
    text-align: right;
}

@media (max-width: 768px) {
    .typing-container {
        white-space: normal;
        word-wrap: break-word;
    }
}
`;

// Inject CSS
const styleSheet = document.createElement('style');
styleSheet.textContent = typingAnimationCSS;
document.head.appendChild(styleSheet);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for hero section to be visible
    const heroSection = document.querySelector('#hero');
    if (heroSection) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    new HeroTypingAnimation();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        observer.observe(heroSection);
    } else {
        // Fallback if hero section not found
        setTimeout(() => {
            new HeroTypingAnimation();
        }, 1000);
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeroTypingAnimation;
}
