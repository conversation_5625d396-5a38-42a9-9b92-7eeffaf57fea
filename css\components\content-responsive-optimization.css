/* ===================================
   CONTENT RESPONSIVE OPTIMIZATION
   Professional content layout for all sections
   =================================== */

/* ===================================
   HERO SECTION CONTENT OPTIMIZATION
   =================================== */

.hero-container-professional {
    display: grid !important;
    grid-template-rows: auto 1fr auto auto auto !important;
    gap: clamp(15px, 3vh, 30px) !important;
    height: 100vh !important;
    max-height: 100vh !important;
    padding: clamp(80px, 12vh, 120px) clamp(20px, 5vw, 80px) clamp(20px, 3vh, 40px) !important;
    align-items: center !important;
    justify-items: center !important;
}

.hero-logo-section {
    grid-row: 1 !important;
    align-self: start !important;
    margin: 0 !important;
}

.hero-title-section {
    grid-row: 2 !important;
    align-self: center !important;
    text-align: center !important;
    margin: 0 !important;
}

.hero-description-section {
    grid-row: 3 !important;
    align-self: center !important;
    text-align: center !important;
    margin: 0 !important;
    max-width: 90% !important;
}

.hero-stats-section {
    grid-row: 4 !important;
    align-self: center !important;
    margin: 0 !important;
    width: 100% !important;
}

.hero-cta-section {
    grid-row: 5 !important;
    align-self: end !important;
    margin: 0 !important;
}

/* Hero Logo Optimization */
.logo-container-advanced {
    width: clamp(80px, 12vw, 150px) !important;
    height: clamp(80px, 12vw, 150px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.hero-logo-main {
    width: 100% !important;
    height: auto !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
}

/* Hero Title Optimization */
.hero-title-professional {
    margin: 0 !important;
    line-height: 1.1 !important;
    text-align: center !important;
}

.hero-title-professional .title-line-1,
.hero-title-professional .title-line-2,
.hero-title-professional .title-line-3 {
    display: block !important;
    margin: 0 !important;
    line-height: 1.1 !important;
}

.hero-title-professional .title-line-1 {
    font-size: clamp(1.8rem, 5vw, 3.5rem) !important;
    margin-bottom: clamp(5px, 1vh, 10px) !important;
}

.hero-title-professional .title-line-2 {
    font-size: clamp(2rem, 5.5vw, 4rem) !important;
    margin-bottom: clamp(5px, 1vh, 10px) !important;
}

.hero-title-professional .title-line-3 {
    font-size: clamp(1.2rem, 3.5vw, 2.5rem) !important;
}

/* Hero Description Optimization */
.hero-description-professional {
    font-size: clamp(0.9rem, 2.2vw, 1.2rem) !important;
    line-height: 1.5 !important;
    margin: 0 !important;
    text-align: center !important;
    max-width: 100% !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
}

/* Hero Stats Optimization */
.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: clamp(10px, 2vw, 20px) !important;
    width: 100% !important;
    max-width: 600px !important;
    margin: 0 auto !important;
}

.stat-item {
    text-align: center !important;
    padding: clamp(8px, 1.5vh, 15px) !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(0, 255, 209, 0.2) !important;
}

.stat-number {
    font-size: clamp(1.2rem, 3vw, 2rem) !important;
    font-weight: 700 !important;
    color: var(--accent) !important;
    line-height: 1 !important;
    margin-bottom: clamp(2px, 0.5vh, 5px) !important;
}

.stat-label {
    font-size: clamp(0.7rem, 1.5vw, 0.9rem) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    line-height: 1.2 !important;
}

/* Hero CTA Optimization */
.cta-buttons-container {
    display: flex !important;
    gap: clamp(10px, 2vw, 20px) !important;
    justify-content: center !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    margin: 0 !important;
}

.cta-primary-advanced,
.cta-secondary-advanced {
    padding: clamp(10px, 1.5vh, 15px) clamp(20px, 3vw, 30px) !important;
    font-size: clamp(0.85rem, 1.8vw, 1rem) !important;
    border-radius: 25px !important;
    white-space: nowrap !important;
    min-width: clamp(120px, 20vw, 160px) !important;
    text-align: center !important;
}

/* ===================================
   SERVICES SECTION CONTENT OPTIMIZATION
   =================================== */

.services-container-professional {
    display: grid !important;
    grid-template-rows: auto 1fr auto !important;
    gap: clamp(20px, 4vh, 40px) !important;
    height: 100vh !important;
    max-height: 100vh !important;
    padding: clamp(60px, 8vh, 100px) clamp(20px, 5vw, 80px) clamp(20px, 3vh, 40px) !important;
}

.services-header {
    grid-row: 1 !important;
    text-align: center !important;
    margin: 0 !important;
}

.services-content {
    grid-row: 2 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    overflow: hidden !important;
}

.services-grid-professional {
    display: grid !important;
    gap: clamp(15px, 2.5vh, 25px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    margin: 0 !important;
}

.service-card-professional {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    padding: clamp(15px, 2.5vh, 25px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    border-radius: 16px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
}

.card-header-professional {
    flex-shrink: 0 !important;
    margin-bottom: clamp(10px, 2vh, 15px) !important;
}

.card-content-professional {
    flex: 1 !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
}

.service-title-professional {
    font-size: clamp(1rem, 2.2vw, 1.3rem) !important;
    line-height: 1.2 !important;
    margin-bottom: clamp(8px, 1.5vh, 12px) !important;
    color: var(--text-light) !important;
}

.service-description-professional {
    font-size: clamp(0.8rem, 1.8vw, 1rem) !important;
    line-height: 1.4 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
    margin-bottom: clamp(10px, 2vh, 15px) !important;
}

.service-features-professional {
    flex-shrink: 0 !important;
}

.feature-item-professional {
    font-size: clamp(0.75rem, 1.5vw, 0.9rem) !important;
    padding: clamp(3px, 0.5vh, 5px) 0 !important;
    color: rgba(255, 255, 255, 0.7) !important;
}

/* ===================================
   RESPONSIVE BREAKPOINTS FOR CONTENT
   =================================== */

/* Desktop Large (1920px+) */
@media (min-width: 1920px) {
    .services-grid-professional {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}

/* Desktop Standard (1200px - 1919px) */
@media (min-width: 1200px) and (max-width: 1919px) {
    .services-grid-professional {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

/* Desktop Small (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .services-grid-professional {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .services-grid-professional {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .hero-description-professional {
        -webkit-line-clamp: 2 !important;
    }

    .service-description-professional {
        -webkit-line-clamp: 2 !important;
    }
}

/* ===================================
   ABOUT SECTION CONTENT OPTIMIZATION
   =================================== */

.about-container {
    display: grid !important;
    grid-template-rows: auto 1fr !important;
    gap: clamp(20px, 4vh, 40px) !important;
    height: 100vh !important;
    max-height: 100vh !important;
    padding: clamp(60px, 8vh, 100px) clamp(20px, 5vw, 80px) clamp(20px, 3vh, 40px) !important;
}

.about-header {
    grid-row: 1 !important;
    text-align: center !important;
    margin: 0 !important;
}

.about-content {
    grid-row: 2 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    overflow: hidden !important;
}

.about-content-grid {
    display: grid !important;
    gap: clamp(20px, 3vh, 30px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    margin: 0 !important;
    align-items: center !important;
}

/* About Cards Optimization */
.story-card,
.values-card,
.timeline-card {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    padding: clamp(20px, 3vh, 30px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    border-radius: 20px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
}

.card-header {
    flex-shrink: 0 !important;
    margin-bottom: clamp(15px, 2.5vh, 20px) !important;
}

.card-title {
    font-size: clamp(1.1rem, 2.5vw, 1.5rem) !important;
    line-height: 1.2 !important;
    margin-bottom: clamp(10px, 1.5vh, 15px) !important;
    color: var(--text-light) !important;
}

.card-content {
    flex: 1 !important;
    overflow: hidden !important;
}

.card-description {
    font-size: clamp(0.85rem, 1.8vw, 1rem) !important;
    line-height: 1.5 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 4 !important;
    -webkit-box-orient: vertical !important;
}

/* Timeline Optimization */
.timeline-container {
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
}

.timeline-item {
    padding: clamp(10px, 2vh, 15px) !important;
    margin-bottom: clamp(8px, 1.5vh, 12px) !important;
    background: rgba(255, 255, 255, 0.03) !important;
    border-radius: 12px !important;
    border-left: 3px solid var(--accent) !important;
}

.timeline-year {
    font-size: clamp(0.9rem, 2vw, 1.1rem) !important;
    font-weight: 600 !important;
    color: var(--accent) !important;
    margin-bottom: clamp(5px, 1vh, 8px) !important;
}

.timeline-content {
    font-size: clamp(0.8rem, 1.6vw, 0.95rem) !important;
    line-height: 1.4 !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

/* ===================================
   PROJECTS SECTION CONTENT OPTIMIZATION
   =================================== */

.projects-container-professional {
    display: grid !important;
    grid-template-rows: auto 1fr !important;
    gap: clamp(20px, 4vh, 40px) !important;
    height: 100vh !important;
    max-height: 100vh !important;
    padding: clamp(60px, 8vh, 100px) clamp(20px, 5vw, 80px) clamp(20px, 3vh, 40px) !important;
}

.projects-header {
    grid-row: 1 !important;
    text-align: center !important;
    margin: 0 !important;
}

.projects-content {
    grid-row: 2 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    overflow: hidden !important;
}

.projects-grid-professional {
    display: grid !important;
    gap: clamp(15px, 2.5vh, 25px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    margin: 0 !important;
}

.project-card-professional {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    padding: clamp(15px, 2.5vh, 25px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    border-radius: 16px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
}

.project-header {
    flex-shrink: 0 !important;
    margin-bottom: clamp(10px, 2vh, 15px) !important;
}

.project-title {
    font-size: clamp(1rem, 2.2vw, 1.3rem) !important;
    line-height: 1.2 !important;
    margin-bottom: clamp(8px, 1.5vh, 12px) !important;
    color: var(--text-light) !important;
}

.project-description {
    font-size: clamp(0.8rem, 1.8vw, 1rem) !important;
    line-height: 1.4 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
    flex: 1 !important;
}

.project-meta {
    flex-shrink: 0 !important;
    margin-top: clamp(10px, 2vh, 15px) !important;
    padding-top: clamp(10px, 2vh, 15px) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.project-status,
.project-year {
    font-size: clamp(0.75rem, 1.5vw, 0.9rem) !important;
    color: rgba(255, 255, 255, 0.7) !important;
}

/* ===================================
   ACTIVITIES SECTION CONTENT OPTIMIZATION
   =================================== */

.activities-container {
    display: grid !important;
    grid-template-rows: auto 1fr !important;
    gap: clamp(20px, 4vh, 40px) !important;
    height: 100vh !important;
    max-height: 100vh !important;
    padding: clamp(60px, 8vh, 100px) clamp(20px, 5vw, 80px) clamp(20px, 3vh, 40px) !important;
}

.activities-header {
    grid-row: 1 !important;
    text-align: center !important;
    margin: 0 !important;
}

.activities-content {
    grid-row: 2 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    overflow: hidden !important;
}

.activities-grid {
    display: grid !important;
    gap: clamp(15px, 2.5vh, 25px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    margin: 0 !important;
}

.activity-card {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    padding: clamp(15px, 2.5vh, 25px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    border-radius: 16px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
}

.activity-header {
    flex-shrink: 0 !important;
    margin-bottom: clamp(10px, 2vh, 15px) !important;
}

.activity-icon {
    width: clamp(40px, 6vw, 60px) !important;
    height: clamp(40px, 6vw, 60px) !important;
    margin-bottom: clamp(10px, 1.5vh, 15px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(0, 255, 209, 0.1) !important;
    border-radius: 50% !important;
}

.activity-title {
    font-size: clamp(1rem, 2.2vw, 1.3rem) !important;
    line-height: 1.2 !important;
    margin-bottom: clamp(8px, 1.5vh, 12px) !important;
    color: var(--text-light) !important;
}

.activity-description {
    font-size: clamp(0.8rem, 1.8vw, 1rem) !important;
    line-height: 1.4 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
    flex: 1 !important;
}

.activity-features {
    flex-shrink: 0 !important;
    margin-top: clamp(10px, 2vh, 15px) !important;
}

.activity-feature {
    font-size: clamp(0.75rem, 1.5vw, 0.9rem) !important;
    color: rgba(255, 255, 255, 0.7) !important;
    padding: clamp(2px, 0.5vh, 4px) 0 !important;
}

/* ===================================
   CONTACT SECTION CONTENT OPTIMIZATION
   =================================== */

.contact-container {
    display: grid !important;
    grid-template-rows: auto 1fr !important;
    gap: clamp(20px, 4vh, 40px) !important;
    height: 100vh !important;
    max-height: 100vh !important;
    padding: clamp(60px, 8vh, 100px) clamp(20px, 5vw, 80px) clamp(20px, 3vh, 40px) !important;
}

.contact-header {
    grid-row: 1 !important;
    text-align: center !important;
    margin: 0 !important;
}

.contact-content {
    grid-row: 2 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    overflow: hidden !important;
}

.contact-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: clamp(20px, 3vh, 40px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    margin: 0 !important;
    align-items: stretch !important;
}

/* Contact Form Optimization */
.contact-form {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    padding: clamp(20px, 3vh, 30px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    border-radius: 20px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
}

.form-group {
    margin-bottom: clamp(12px, 2vh, 18px) !important;
}

.form-group label {
    font-size: clamp(0.85rem, 1.6vw, 1rem) !important;
    color: var(--text-light) !important;
    margin-bottom: clamp(5px, 1vh, 8px) !important;
    display: block !important;
}

.form-group input,
.form-group textarea {
    width: 100% !important;
    padding: clamp(10px, 1.5vh, 15px) !important;
    font-size: clamp(0.85rem, 1.6vw, 1rem) !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    color: var(--text-light) !important;
    box-sizing: border-box !important;
}

.form-group textarea {
    height: clamp(80px, 12vh, 120px) !important;
    resize: none !important;
}

.submit-button {
    padding: clamp(12px, 2vh, 16px) clamp(20px, 3vw, 30px) !important;
    font-size: clamp(0.9rem, 1.8vw, 1.1rem) !important;
    background: linear-gradient(135deg, var(--primary), var(--accent)) !important;
    border: none !important;
    border-radius: 25px !important;
    color: var(--text-light) !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin-top: clamp(10px, 2vh, 15px) !important;
}

/* Contact Info Optimization */
.contact-info {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    padding: clamp(20px, 3vh, 30px) !important;
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    border-radius: 20px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
}

.contact-item {
    margin-bottom: clamp(15px, 2.5vh, 20px) !important;
    padding: clamp(12px, 2vh, 16px) !important;
    background: rgba(255, 255, 255, 0.03) !important;
    border-radius: 12px !important;
    border-left: 3px solid var(--accent) !important;
}

.contact-item-title {
    font-size: clamp(0.9rem, 1.8vw, 1.1rem) !important;
    font-weight: 600 !important;
    color: var(--accent) !important;
    margin-bottom: clamp(5px, 1vh, 8px) !important;
}

.contact-item-content {
    font-size: clamp(0.8rem, 1.6vw, 0.95rem) !important;
    line-height: 1.4 !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Mobile Large (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {

    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid {
        grid-template-columns: 1fr !important;
        overflow-y: auto !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
    }

    .services-grid-professional::-webkit-scrollbar,
    .projects-grid-professional::-webkit-scrollbar,
    .activities-grid::-webkit-scrollbar {
        display: none !important;
    }

    .about-content-grid {
        grid-template-columns: 1fr !important;
        overflow-y: auto !important;
    }

    .contact-grid {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }

    .stats-grid {
        grid-template-columns: 1fr !important;
        gap: 8px !important;
    }

    .cta-buttons-container {
        flex-direction: column !important;
        gap: 10px !important;
    }

    .cta-primary-advanced,
    .cta-secondary-advanced {
        width: 100% !important;
        max-width: 250px !important;
    }
}

/* Mobile Small (320px - 575px) */
@media (max-width: 575px) {
    .hero-container-professional {
        padding: clamp(70px, 10vh, 90px) 15px clamp(15px, 2vh, 25px) !important;
        gap: clamp(10px, 2vh, 20px) !important;
    }

    .services-container-professional,
    .about-container,
    .projects-container-professional,
    .activities-container,
    .contact-container {
        padding: clamp(50px, 6vh, 70px) 15px clamp(15px, 2vh, 25px) !important;
        gap: clamp(15px, 3vh, 25px) !important;
    }

    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid,
    .about-content-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
        overflow-y: auto !important;
    }

    .contact-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }

    .service-card-professional,
    .project-card-professional,
    .activity-card,
    .story-card,
    .values-card,
    .timeline-card {
        padding: 15px !important;
    }

    .hero-description-professional,
    .service-description-professional,
    .project-description,
    .activity-description,
    .card-description {
        -webkit-line-clamp: 2 !important;
    }

    .stats-grid {
        grid-template-columns: 1fr !important;
        gap: 8px !important;
    }

    .cta-buttons-container {
        flex-direction: column !important;
        gap: 10px !important;
    }

    .cta-primary-advanced,
    .cta-secondary-advanced {
        width: 100% !important;
        max-width: 280px !important;
    }
}

/* Grid Template Columns for Different Breakpoints */
@media (min-width: 1920px) {

    .services-grid-professional,
    .projects-grid-professional {
        grid-template-columns: repeat(4, 1fr) !important;
    }

    .activities-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .about-content-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 1200px) and (max-width: 1919px) {

    .services-grid-professional,
    .projects-grid-professional {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .activities-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .about-content-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {

    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .about-content-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 768px) and (max-width: 991px) {

    .services-grid-professional,
    .projects-grid-professional,
    .activities-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .about-content-grid {
        grid-template-columns: 1fr !important;
    }

    .contact-grid {
        grid-template-columns: 1fr !important;
    }
}
}

.services-grid-professional::-webkit-scrollbar {
    display: none !important;
}

.stats-grid {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
}

.cta-buttons-container {
    flex-direction: column !important;
    gap: 10px !important;
}

.cta-primary-advanced,
.cta-secondary-advanced {
    width: 100% !important;
    max-width: 250px !important;
}
}

/* Mobile Small (320px - 575px) */
@media (max-width: 575px) {
    .hero-container-professional {
        padding: clamp(70px, 10vh, 90px) 15px clamp(15px, 2vh, 25px) !important;
        gap: clamp(10px, 2vh, 20px) !important;
    }

    .services-container-professional {
        padding: clamp(50px, 6vh, 70px) 15px clamp(15px, 2vh, 25px) !important;
        gap: clamp(15px, 3vh, 25px) !important;
    }

    .services-grid-professional {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }

    .service-card-professional {
        padding: 15px !important;
    }

    .hero-description-professional {
        -webkit-line-clamp: 2 !important;
    }

    .service-description-professional {
        -webkit-line-clamp: 2 !important;
    }
}